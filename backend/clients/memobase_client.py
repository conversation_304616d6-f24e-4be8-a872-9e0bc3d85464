"""
Memobase客户端模块
使用Memobase SDK进行用户记忆管理
"""

import logging
from typing import Dict, List, Optional, Any
from config import Config

logger = logging.getLogger(__name__)


class MemobaseClient:
    """Memobase客户端"""

    def __init__(self):
        """初始化Memobase客户端"""
        self.config = Config.MEMOBASE_CONFIG
        self.project_url = self.config.get('project_url', 'http://localhost:8019')
        self.api_key = self.config.get('api_key', 'secret')
        self.project_id = self.config.get('project_id', 'memobase_dev')

        # 初始化Memobase客户端
        self._init_client()

    def _init_client(self):
        """初始化Memobase客户端"""
        try:
            # 使用本地Memobase客户端
            from memobase.core.entry import MemoBaseClient

            self.client = MemoBaseClient(
                project_url=self.project_url,
                api_key=self.api_key
            )

            # 测试连接
            try:
                self.client.ping()
                logger.info(f"✅ Memobase客户端初始化成功: {self.project_url}")
            except Exception as e:
                logger.warning(f"⚠️ Memobase连接测试失败，但继续初始化: {e}")

        except ImportError:
            logger.error("❌ Memobase SDK未安装，请运行: pip install memobase")
            raise
        except Exception as e:
            logger.error(f"❌ Memobase客户端初始化失败: {e}")
            raise

    def get_or_create_user(self, user_id: str, user_data: Dict = None) -> Any:
        """获取或创建用户"""
        try:
            # 首先检查是否有映射的Memobase UUID
            actual_user_id = user_id

            # 检查用户ID映射（排除虚拟人ID）
            try:
                from services.persona_id_manager import get_persona_id_manager
                persona_manager = get_persona_id_manager()
                current_persona_id = persona_manager.get_persona_id()

                if user_id != current_persona_id and not user_id.startswith('12345678-1234-5678-9abc'):
                    # 这是普通用户ID，检查映射
                    from services.user_id_mapping_manager import get_user_id_mapping_manager
                    mapping_manager = get_user_id_mapping_manager()
                    mapped_uuid = mapping_manager.get_memobase_uuid(user_id)

                    if mapped_uuid:
                        actual_user_id = mapped_uuid
                        logger.debug(f"📋 使用映射的UUID: {user_id} -> {actual_user_id}")

            except Exception as e:
                logger.debug(f"检查用户ID映射时出错: {e}")

            # 尝试获取现有用户
            try:
                user = self.client.get_user(actual_user_id)
                logger.debug(f"📋 获取现有用户: {actual_user_id}")
                return user
            except:
                # 用户不存在，创建新用户
                user_data = user_data or {"user_id": user_id}
                actual_uid = self.client.add_user(user_data)
                user = self.client.get_user(actual_uid)

                # 记录实际的UUID和我们期望的UUID的映射关系
                logger.info(f"👤 创建新用户: 期望ID={user_id}, 实际ID={actual_uid}")

                # 如果ID不一致，保存实际的UUID映射
                if actual_uid != user_id:
                    logger.warning(f"⚠️ Memobase生成的UUID与期望不符: 期望={user_id}, 实际={actual_uid}")

                    # 判断是虚拟人ID还是用户ID，分别处理
                    try:
                        # 检查是否是虚拟人ID（通过导入虚拟人ID管理器检查）
                        from services.persona_id_manager import get_persona_id_manager
                        persona_manager = get_persona_id_manager()
                        current_persona_id = persona_manager.get_persona_id()

                        if user_id == current_persona_id or user_id.startswith('12345678-1234-5678-9abc'):
                            # 这是虚拟人ID，保存到虚拟人ID管理器
                            persona_manager.save_persona_id(actual_uid)
                            logger.info(f"💾 已保存虚拟人Memobase UUID: {actual_uid}")
                        else:
                            # 这是用户ID，保存到用户ID映射管理器
                            from services.user_id_mapping_manager import get_user_id_mapping_manager
                            mapping_manager = get_user_id_mapping_manager()
                            mapping_manager.save_mapping(user_id, actual_uid)
                            logger.info(f"💾 已保存用户ID映射: {user_id} -> {actual_uid}")

                    except Exception as e:
                        logger.error(f"💥 保存UUID映射失败: {e}")

                return user

        except Exception as e:
            logger.error(f"💥 获取或创建用户失败 - 用户: {user_id}, 错误: {e}")
            raise

    def update_user(self, user_id: str, user_data: Dict) -> bool:
        """更新用户信息"""
        try:
            self.client.update_user(user_id, user_data)
            logger.info(f"📝 更新用户信息: {user_id}")
            return True
        except Exception as e:
            logger.error(f"💥 更新用户信息失败 - 用户: {user_id}, 错误: {e}")
            return False

    def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        try:
            self.client.delete_user(user_id)
            logger.info(f"🗑️ 删除用户: {user_id}")
            return True
        except Exception as e:
            logger.error(f"💥 删除用户失败 - 用户: {user_id}, 错误: {e}")
            return False

    def insert_chat_data(self, user, messages: List[Dict]) -> str:
        """插入聊天数据"""
        try:
            from memobase.core.blob import ChatBlob

            chat_blob = ChatBlob(messages=messages)
            blob_id = user.insert(chat_blob)
            
            logger.debug(f"💬 插入聊天数据: {len(messages)} 条消息")
            return blob_id
            
        except Exception as e:
            logger.error(f"💥 插入聊天数据失败: {e}")
            raise

    def flush_user_memory(self, user) -> bool:
        """刷新用户记忆"""
        try:
            user.flush()
            logger.info("🔄 用户记忆已刷新")
            return True
        except Exception as e:
            logger.error(f"💥 刷新用户记忆失败: {e}")
            return False

    def get_user_profile(self, user, need_json: bool = False) -> List[Dict]:
        """获取用户画像"""
        try:
            profile = user.profile(need_json=need_json)
            logger.debug(f"👤 获取用户画像: {len(profile) if isinstance(profile, list) else 'JSON格式'}")
            return profile
        except Exception as e:
            logger.error(f"💥 获取用户画像失败: {e}")
            return [] if not need_json else {}

    def get_user_context(self, user, max_token_size: int = 500, prefer_topics: List[str] = None,
                        chats: List[Dict] = None, optimize_tokens: bool = True) -> str:
        """
        获取用户上下文

        Args:
            user: 用户对象
            max_token_size: 最大token数量
            prefer_topics: 优先话题
            chats: 聊天历史（用于上下文相关性）
            optimize_tokens: 是否优化token使用
        """
        try:
            # 如果启用token优化，动态调整参数
            if optimize_tokens:
                # 根据聊天历史长度调整token大小
                if chats and len(chats) > 5:
                    max_token_size = min(max_token_size, 300)  # 减少token使用
                    logger.debug(f"🔧 Token优化: 检测到长对话历史，减少上下文token至 {max_token_size}")

                # 限制话题数量以减少token消耗
                if prefer_topics and len(prefer_topics) > 3:
                    prefer_topics = prefer_topics[:3]
                    logger.debug(f"🔧 Token优化: 限制话题数量至 {len(prefer_topics)}")

            context = user.context(
                max_token_size=max_token_size,
                prefer_topics=prefer_topics or [],
                chats=chats
            )

            # 记录token使用情况
            char_count = len(context)
            estimated_tokens = char_count // 3  # 粗略估算token数量（中文约3字符=1token）
            logger.info(f"📄 获取用户上下文: {char_count} 字符, 预估 {estimated_tokens} tokens")

            # 如果上下文过长，发出警告
            if estimated_tokens > max_token_size * 1.2:
                logger.warning(f"⚠️ 上下文可能超出预期token限制: 预估 {estimated_tokens} > 限制 {max_token_size}")

            return context
        except Exception as e:
            logger.error(f"💥 获取用户上下文失败: {e}")
            return ""

    def get_usage_stats(self) -> Dict:
        """获取使用统计"""
        try:
            usage = self.client.get_usage()
            logger.info(f"📊 Memobase使用统计: {usage}")
            return usage
        except Exception as e:
            logger.error(f"💥 获取使用统计失败: {e}")
            return {}

    def ping(self) -> bool:
        """测试连接"""
        try:
            return self.client.ping()
        except Exception as e:
            logger.error(f"💥 连接测试失败: {e}")
            return False


# 全局Memobase客户端实例
_memobase_client = None


def get_memobase_client() -> MemobaseClient:
    """获取全局Memobase客户端实例"""
    global _memobase_client
    if _memobase_client is None:
        _memobase_client = MemobaseClient()
    return _memobase_client
