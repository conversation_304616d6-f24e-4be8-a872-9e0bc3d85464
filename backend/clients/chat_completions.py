"""
Chat Completions Client - 统一的LLM API调用客户端
支持火山引擎和其他LLM服务提供商
"""

import json
import requests
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class ChatCompletionsClient(ABC):
    """Chat Completions客户端抽象基类"""
    
    @abstractmethod
    def chat_completions(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送聊天完成请求
        
        Args:
            messages: 消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
            
        Returns:
            API响应结果
        """
        pass


class VolcengineClient(ChatCompletionsClient):
    """火山引擎LLM客户端"""
    
    def __init__(self, access_key: str, secret_key: str, region: str = "cn-beijing"):
        self.access_key = access_key
        self.secret_key = secret_key
        self.region = region
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3"
    
    def chat_completions(
        self,
        messages: List[Dict[str, str]],
        model: str = "doubao-lite-4k",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ) -> Dict[str, Any]:
        """
        调用火山引擎Chat Completions API
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        
        try:
            # 构建请求数据
            data = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False,
                **kwargs
            }
            
            # 记录请求日志
            logger.info(f"🌐 火山引擎API请求 [{request_id}]")
            logger.info(f"📋 请求数据: model={model}, messages={len(messages)}条, temperature={temperature}")
            logger.info(f"💬 最后一条消息: {messages[-1]['content'][:100]}..." if messages else "无消息")
            
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self._get_access_token()}"
            }
            
            # 发送请求
            logger.info(f"🚀 发送请求到: {self.base_url}/chat/completions")
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                logger.info(f"✅ 火山引擎API响应成功 [{request_id}]")
                logger.info(f"📝 响应内容: {content}")
                logger.info(f"📊 响应统计: {len(content)} 字符")
                
                return {
                    "success": True,
                    "content": content,
                    "raw_response": result,
                    "request_id": request_id
                }
            else:
                logger.error(f"❌ 火山引擎API错误 [{request_id}]")
                logger.error(f"状态码: {response.status_code}")
                logger.error(f"错误信息: {response.text}")
                
                return {
                    "success": False,
                    "error": f"API错误: {response.status_code}",
                    "error_detail": response.text,
                    "request_id": request_id
                }
                
        except requests.exceptions.Timeout:
            logger.error(f"⏰ 火山引擎API超时 [{request_id}]")
            return {
                "success": False,
                "error": "请求超时",
                "request_id": request_id
            }
        except requests.exceptions.ConnectionError:
            logger.error(f"🔌 火山引擎API连接错误 [{request_id}]")
            return {
                "success": False,
                "error": "连接错误",
                "request_id": request_id
            }
        except Exception as e:
            logger.error(f"💥 火山引擎API异常 [{request_id}]: {str(e)}")
            return {
                "success": False,
                "error": f"未知错误: {str(e)}",
                "request_id": request_id
            }
    
    def _get_access_token(self) -> str:
        """
        获取访问令牌
        注意：这里需要根据火山引擎的实际认证方式进行调整
        """
        return self.access_key






def create_client(provider: str = "volcengine", **config) -> ChatCompletionsClient:
    """
    创建Chat Completions客户端

    Args:
        provider: 服务提供商 (仅支持 "volcengine")
        **config: 配置参数

    Returns:
        客户端实例
    """
    if provider == "volcengine":
        return VolcengineClient(
            access_key=config.get('access_key', ''),
            secret_key=config.get('secret_key', ''),
            region=config.get('region', 'cn-beijing')
        )
    else:
        raise ValueError(f"不支持的服务提供商: {provider}，仅支持 volcengine")
