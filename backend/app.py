from flask import Flask, request, jsonify, send_from_directory, render_template_string
from flask_cors import CORS
import os
import sys
from datetime import datetime

# 设置UTF-8编码
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 配置日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(pathname)s:%(lineno)d - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

from services.conversation_engine import ConversationEngine
from services.user_manager import UserManager
from models.database import DatabaseManager
from routes.persona_admin import persona_admin_bp
from routes.user_memory_admin import user_memory_admin_bp
from routes.user_memory_query import user_memory_query_bp
from routes.config_display import config_display_bp

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 注册蓝图
app.register_blueprint(persona_admin_bp)
app.register_blueprint(user_memory_admin_bp)
app.register_blueprint(user_memory_query_bp)
app.register_blueprint(config_display_bp)

# 初始化服务
conversation_engine = ConversationEngine()
db = DatabaseManager()
user_manager = UserManager(db)

# 确保数据库目录存在
os.makedirs(os.path.dirname(db.db_path), exist_ok=True)

@app.route('/')
def index():
    """提供前端页面"""
    return send_from_directory('../frontend', 'index.html')

@app.route('/admin')
def admin_redirect():
    """重定向到新的管理界面"""
    return send_from_directory('../frontend/admin', 'index.html')

@app.route('/admin/')
def admin_index():
    """提供管理界面"""
    return send_from_directory('../frontend/admin', 'index.html')

@app.route('/admin/old')
def admin_old():
    """旧的管理功能总览页面"""
    return render_template_string("""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟人恋爱陪伴系统 - 管理中心</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #333;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        .admin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        .admin-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-align: center;
        }
        .admin-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        .admin-card .icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .admin-card h3 {
            color: #333;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        .admin-card p {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }
        .admin-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .admin-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .back-button {
            background: linear-gradient(45deg, #95e1d3, #fce38a);
            color: #333;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }
        .back-button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-button">← 返回主页</a>

        <div class="header">
            <h1>🛠️ 管理中心</h1>
            <p>沈沐心（沐沐）虚拟人恋爱陪伴系统管理功能</p>
        </div>

        <div class="admin-grid">
            <div class="admin-card">
                <div class="icon">👤</div>
                <h3>人物配置展示</h3>
                <p>查看沐沐的详细人物设定、情感特征、恋爱偏好、时间感知等完整配置信息</p>
                <a href="/admin/config/dashboard" class="admin-button">查看配置</a>
            </div>

            <div class="admin-card">
                <div class="icon">🧠</div>
                <h3>虚拟人记忆管理</h3>
                <p>管理沐沐的个人记忆，包括记忆初始化、记忆查询和记忆统计功能</p>
                <a href="/admin/persona/dashboard" class="admin-button">管理记忆</a>
            </div>

            <div class="admin-card">
                <div class="icon">🧪</div>
                <h3>用户记忆测试</h3>
                <p>测试用户记忆功能，包括记忆提取、相关记忆检索和用户ID映射管理</p>
                <a href="/admin/user-memory/dashboard" class="admin-button">测试功能</a>
            </div>

            <div class="admin-card">
                <div class="icon">🔍</div>
                <h3>用户记忆查询</h3>
                <p>查询chat.db中每个用户的记忆数据和对话记录，支持搜索和筛选</p>
                <a href="/admin/user-memory-query/dashboard" class="admin-button">查询记忆</a>
            </div>
        </div>
    </div>
</body>
</html>
    """)

@app.route('/admin/<path:filename>')
def admin_static_files(filename):
    """提供管理界面静态文件"""
    return send_from_directory('../frontend/admin', filename)

@app.route('/<path:filename>')
def static_files(filename):
    """提供静态文件"""
    return send_from_directory('../frontend', filename)

@app.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天消息"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        data = request.get_json()

        if not data or 'message' not in data:
            return jsonify({'error': '消息内容不能为空'}), 400

        message = data['message'].strip()

        if not message:
            return jsonify({'error': '消息内容不能为空'}), 400

        # 使用会话中的用户ID
        user_id = user['user_id']

        # 处理消息
        result = conversation_engine.process_message(user_id, message)

        return jsonify({
            'success': True,
            'user_id': user_id,
            'response': result['response'],
            'response_segments': result.get('response_segments', [result['response']]),
            'affection_level': result['affection_level'],
            'affection_change': result['affection_change'],
            'emotion': result['emotion'],
            'memories_extracted': result['memories_extracted'],
            'persona_activity': result['persona_activity'],
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"Chat error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误，请稍后重试'
        }), 500

@app.route('/api/greeting', methods=['POST'])
def greeting():
    """获取问候消息"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        # 使用会话中的用户ID
        user_id = user['user_id']

        result = conversation_engine.get_greeting_message(user_id)

        return jsonify({
            'success': True,
            'user_id': user_id,
            'response': result['response'],
            'affection_level': result['affection_level'],
            'persona_activity': result['persona_activity'],
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"Greeting error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误，请稍后重试'
        }), 500

@app.route('/api/user/info', methods=['GET'])
def get_user_info():
    """获取用户信息"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        user_id = user['user_id']

        user_data = db.get_user_or_create(user_id)
        affection_level = db.get_current_affection(user_id)
        recent_conversations = db.get_recent_conversations(user_id, limit=5)
        memories = db.get_relevant_memories(user_id, limit=10)

        return jsonify({
            'success': True,
            'user': user_data,
            'affection_level': affection_level,
            'recent_conversations': recent_conversations,
            'memories': memories
        })

    except Exception as e:
        print(f"Get user info error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取用户信息失败'
        }), 500

@app.route('/api/memories', methods=['GET'])
def get_memories():
    """获取用户记忆"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        user_id = user['user_id']

        memories = conversation_engine.memory_manager.get_relevant_memories(user_id, limit=20)

        return jsonify({
            'success': True,
            'memories': memories
        })

    except Exception as e:
        print(f"Get memories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取记忆失败'
        }), 500

@app.route('/api/memories/summary', methods=['GET'])
def get_memory_summary():
    """获取用户记忆摘要"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        user_id = user['user_id']

        # 使用Memobase获取用户上下文作为摘要
        summary = conversation_engine.memory_manager.get_user_context(user_id, max_token_size=300)

        return jsonify({
            'success': True,
            'summary': summary
        })

    except Exception as e:
        print(f"Get memory summary error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取记忆摘要失败'
        }), 500

# 新增记忆管理API
@app.route('/api/memories/stats', methods=['GET'])
def get_memory_stats():
    """获取记忆统计信息"""
    try:
        # 获取用户记忆统计
        user_stats = conversation_engine.memory_manager.get_memory_statistics()

        # 获取个人记忆统计
        persona_stats = conversation_engine.persona_memory_manager.get_memory_stats()

        return jsonify({
            'success': True,
            'user_memories': user_stats,
            'persona_memories': persona_stats
        })
    except Exception as e:
        print(f"Get memory stats error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/memories/search', methods=['GET'])
def search_memories():
    """搜索用户记忆"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        user_id = user['user_id']
        search_query = request.args.get('query', '')
        limit = int(request.args.get('limit', 20))

        if search_query:
            memories = conversation_engine.memory_manager.get_relevant_memories(
                user_id, query_text=search_query, limit=limit
            )
        else:
            memories = conversation_engine.memory_manager.get_relevant_memories(
                user_id, limit=limit
            )

        return jsonify({
            'success': True,
            'memories': memories
        })
    except Exception as e:
        print(f"Search memories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/memories/persona', methods=['GET'])
def get_persona_memories():
    """获取个人记忆"""
    try:
        search_query = request.args.get('query', '')
        memory_type = request.args.get('type', None)
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        max_sharing_level = int(request.args.get('max_sharing_level', 5))

        if search_query:
            # 搜索相关记忆
            memories = conversation_engine.persona_memory_manager.get_relevant_persona_memories(
                search_query, user_affection=100, limit=page_size
            )
            result = {
                'success': True,
                'memories': memories,
                'pagination': {
                    'page': 1,
                    'page_size': len(memories),
                    'total': len(memories),
                    'total_pages': 1
                }
            }
        else:
            # 获取所有记忆（简化版本，不支持分页）
            memories = conversation_engine.persona_memory_manager.get_relevant_persona_memories(
                "", user_affection=100, limit=page_size
            )
            result = {
                'success': True,
                'memories': memories,
                'pagination': {
                    'page': page,
                    'page_size': len(memories),
                    'total': len(memories),
                    'total_pages': 1
                }
            }

        return jsonify(result)
    except Exception as e:
        print(f"Get persona memories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/memories/clear', methods=['DELETE'])
def clear_user_memories():
    """清除当前用户的所有记忆（Memobase暂不支持）"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        # Memobase暂不支持清除记忆功能
        return jsonify({
            'success': False,
            'message': 'Memobase暂不支持清除记忆功能'
        })
    except Exception as e:
        print(f"Clear memories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 用户管理API
@app.route('/api/users/register', methods=['POST'])
def register_user():
    """用户注册"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        nickname = data.get('nickname', '').strip()
        email = data.get('email', '').strip()

        if not username:
            return jsonify({'error': '用户名不能为空'}), 400

        # 创建用户
        user = user_manager.create_user(
            username=username,
            password=password if password else None,
            nickname=nickname if nickname else None,
            email=email if email else None
        )

        return jsonify({
            'success': True,
            'message': '用户注册成功',
            'user': user
        })

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        print(f"Register error: {str(e)}")
        return jsonify({'error': '注册失败，请稍后重试'}), 500

@app.route('/api/users/login', methods=['POST'])
def login_user():
    """用户登录"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        username = data.get('username', '').strip()
        password = data.get('password', '').strip()

        if not username or not password:
            return jsonify({'error': '用户名和密码不能为空'}), 400

        # 用户认证
        user = user_manager.authenticate_user(username, password)
        if not user:
            return jsonify({'error': '用户名或密码错误'}), 401

        # 创建会话
        session_token = user_manager.create_session(user['user_id'])

        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': user,
            'session_token': session_token
        })

    except Exception as e:
        print(f"Login error: {str(e)}")
        return jsonify({'error': '登录失败，请稍后重试'}), 500

@app.route('/api/users/logout', methods=['POST'])
def logout_user():
    """用户登出"""
    try:
        # 从请求头获取会话令牌
        session_token = request.headers.get('Authorization')
        if session_token and session_token.startswith('Bearer '):
            session_token = session_token[7:]  # 移除 "Bearer " 前缀

        if not session_token:
            return jsonify({'error': '会话令牌不能为空'}), 400

        # 登出用户
        success = user_manager.logout_user(session_token)

        if success:
            return jsonify({
                'success': True,
                'message': '登出成功'
            })
        else:
            return jsonify({'error': '登出失败'}), 400

    except Exception as e:
        print(f"Logout error: {str(e)}")
        return jsonify({'error': '登出失败，请稍后重试'}), 500

@app.route('/api/users/profile', methods=['GET'])
def get_user_profile():
    """获取用户资料"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        # 获取详细用户信息
        user_info = user_manager.get_user_info(user['user_id'])
        if not user_info:
            return jsonify({'error': '用户不存在'}), 404

        # 获取用户统计信息
        affection_level = db.get_current_affection(user['user_id'])
        recent_conversations = db.get_recent_conversations(user['user_id'], limit=5)
        memory_summary = conversation_engine.memory_manager.get_user_context(user['user_id'], max_token_size=200)

        return jsonify({
            'success': True,
            'user': user_info,
            'stats': {
                'affection_level': affection_level,
                'conversation_count': len(recent_conversations),
                'memory_summary': memory_summary
            }
        })

    except Exception as e:
        print(f"Get profile error: {str(e)}")
        return jsonify({'error': '获取用户资料失败'}), 500

@app.route('/api/users/profile', methods=['PUT'])
def update_user_profile():
    """更新用户资料"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        # 更新用户信息
        success = user_manager.update_user_info(user['user_id'], **data)

        if success:
            return jsonify({
                'success': True,
                'message': '用户资料更新成功'
            })
        else:
            return jsonify({'error': '更新失败'}), 400

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        print(f"Update profile error: {str(e)}")
        return jsonify({'error': '更新用户资料失败'}), 500

def validate_session():
    """验证会话中间件"""
    session_token = request.headers.get('Authorization')
    if session_token and session_token.startswith('Bearer '):
        session_token = session_token[7:]  # 移除 "Bearer " 前缀

    if not session_token:
        return None

    return user_manager.validate_session(session_token)

# 管理API
@app.route('/api/admin/stats', methods=['GET'])
def get_admin_stats():
    """获取管理统计数据"""
    try:
        # 获取用户统计
        users = db.get_all_users()
        total_users = len(users)

        # 获取对话统计
        total_conversations = 0
        avg_affection = 0
        if users:
            affection_sum = 0
            for user in users:
                conversations = db.get_recent_conversations(user['user_id'], limit=1000)
                total_conversations += len(conversations)
                affection = db.get_current_affection(user['user_id'])
                affection_sum += affection
            avg_affection = round(affection_sum / len(users), 1)

        # 获取记忆统计
        memory_stats = conversation_engine.memory_manager.get_memory_statistics()
        total_memories = memory_stats.get('total', 0)

        return jsonify({
            'success': True,
            'total_users': total_users,
            'total_conversations': total_conversations,
            'total_memories': total_memories,
            'avg_affection': avg_affection
        })
    except Exception as e:
        print(f"Get admin stats error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/admin/users', methods=['GET'])
def get_admin_users():
    """获取所有用户列表"""
    try:
        users = db.get_all_users()

        # 导入用户ID映射管理器
        from services.user_id_mapping_manager import get_user_id_mapping_manager
        mapping_manager = get_user_id_mapping_manager()

        # 为每个用户添加额外信息
        for user in users:
            user['affection_level'] = db.get_current_affection(user['user_id'])
            recent_conversations = db.get_recent_conversations(user['user_id'], limit=1)
            user['last_active'] = recent_conversations[0]['timestamp'] if recent_conversations else user['created_at']

            # 获取Memobase ID
            memobase_uuid = mapping_manager.get_memobase_uuid(user['user_id'])
            user['memobase_id'] = memobase_uuid if memobase_uuid else '未映射'

        return jsonify(users)
    except Exception as e:
        print(f"Get admin users error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/admin/activity', methods=['GET'])
def get_admin_activity():
    """获取最近活动"""
    try:
        # 这里可以实现获取最近活动的逻辑
        # 暂时返回模拟数据
        activities = [
            {
                'title': '新用户注册',
                'description': '用户 test_user 完成注册',
                'timestamp': datetime.now().isoformat()
            },
            {
                'title': '对话记录',
                'description': '用户与沈沐心进行了对话',
                'timestamp': datetime.now().isoformat()
            }
        ]

        return jsonify(activities)
    except Exception as e:
        print(f"Get admin activity error: {str(e)}")
        return jsonify([])

@app.route('/api/admin/config/persona', methods=['GET'])
def get_persona_config():
    """获取人设配置"""
    try:
        # 这里可以返回人设配置信息
        config = {
            'name': '沈沐心',
            'age': '25岁',
            'profession': '心理咨询师',
            'personality': '温柔、善解人意、专业、有耐心'
        }

        return jsonify(config)
    except Exception as e:
        print(f"Get persona config error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/admin/memories/user/<user_id>', methods=['GET'])
def get_admin_user_memories(user_id):
    """管理界面获取用户记忆"""
    try:
        limit = int(request.args.get('limit', 50))

        # 直接调用记忆管理器，不需要会话验证
        memories = conversation_engine.memory_manager.get_relevant_memories(
            user_id, limit=limit
        )

        return jsonify({
            'success': True,
            'memories': memories
        })
    except Exception as e:
        print(f"Get admin user memories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/admin/memories/persona', methods=['GET'])
def get_admin_persona_memories():
    """管理界面获取虚拟人记忆"""
    try:
        search_query = request.args.get('query', '')
        category = request.args.get('category', '')
        limit = int(request.args.get('limit', 50))

        # 构建搜索查询
        if category and search_query:
            query = f"{category} {search_query}"
        elif category:
            query = category
        elif search_query:
            query = search_query
        else:
            query = ""

        memories = conversation_engine.persona_memory_manager.get_relevant_persona_memories(
            query, user_affection=100, limit=limit
        )

        # 为记忆添加类别信息（如果可能的话）
        for memory in memories:
            if not memory.get('category'):
                # 尝试从内容中推断类别
                content = memory.get('content', '').lower()
                if any(word in content for word in ['姓名', '年龄', '身高', '体重', '生日']):
                    memory['category'] = 'basic_info'
                elif any(word in content for word in ['喜欢', '爱好', '偏好', '喜爱']):
                    memory['category'] = 'preferences'
                elif any(word in content for word in ['经历', '体验', '去过', '做过']):
                    memory['category'] = 'experiences'
                elif any(word in content for word in ['朋友', '家人', '关系', '父母']):
                    memory['category'] = 'relationships'
                elif any(word in content for word in ['情感', '心情', '感受', '情绪']):
                    memory['category'] = 'emotions'
                else:
                    memory['category'] = '未分类'

        return jsonify({
            'success': True,
            'memories': memories
        })
    except Exception as e:
        print(f"Get admin persona memories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/admin/memories/stats', methods=['GET'])
def get_admin_memory_stats():
    """管理界面获取记忆统计"""
    try:
        # 获取用户记忆统计
        user_stats = conversation_engine.memory_manager.get_memory_statistics()

        # 获取个人记忆统计
        persona_stats = conversation_engine.persona_memory_manager.get_memory_stats()

        # 获取活跃用户数
        users = db.get_all_users()
        active_users = len([user for user in users if user.get('last_active')])

        return jsonify({
            'success': True,
            'user_memories': user_stats,
            'persona_memories': persona_stats,
            'active_users': active_users
        })
    except Exception as e:
        print(f"Get admin memory stats error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



# ==================== Memobase代理转发 ====================

@app.route('/api/memobase/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
def memobase_proxy(path):
    """Memobase API代理转发"""
    try:
        import requests
        from config import Config

        memobase_config = Config.MEMOBASE_CONFIG
        base_url = memobase_config.get('project_url', 'http://localhost:8019')
        api_key = memobase_config.get('api_key', 'secret')

        # 构建目标URL
        target_url = f"{base_url}/api/{path}"

        # 准备请求头
        headers = dict(request.headers)
        # 移除可能冲突的头部
        headers.pop('Host', None)
        headers.pop('Content-Length', None)

        # 添加认证头 - Memobase使用Bearer token认证
        if api_key:
            headers['Authorization'] = f'Bearer {api_key}'

        # 准备请求参数
        params = dict(request.args)

        # 准备请求体
        data = None
        json_data = None
        if request.is_json:
            json_data = request.get_json()
        elif request.data:
            data = request.data

        # 发送请求
        response = requests.request(
            method=request.method,
            url=target_url,
            headers=headers,
            params=params,
            data=data,
            json=json_data,
            timeout=30
        )

        # 返回响应
        return response.content, response.status_code, dict(response.headers)

    except Exception as e:
        print(f"Memobase proxy error: {str(e)}")
        return jsonify({
            'error': f'代理转发失败: {str(e)}'
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': '虚拟人恋爱陪伴系统'
    })

if __name__ == '__main__':
    print("启动虚拟人恋爱陪伴系统...")
    print("前端地址: http://localhost:8080")
    print("API地址: http://localhost:8080/api")

    app.run(
        host='0.0.0.0',
        port=8080,
        debug=True
    )
