"""
用户记忆管理路由
提供用户记忆管理的管理接口
"""

import logging
from flask import Blueprint, jsonify, request
from services.memobase_memory_manager import MemobaseMemoryManager
from services.user_id_mapping_manager import get_user_id_mapping_manager

logger = logging.getLogger(__name__)

# 创建蓝图
user_memory_admin_bp = Blueprint('user_memory_admin', __name__, url_prefix='/admin/user-memory')


@user_memory_admin_bp.route('/mappings')
def get_user_mappings():
    """获取用户ID映射信息"""
    try:
        mapping_manager = get_user_id_mapping_manager()
        
        # 获取所有映射
        mappings = mapping_manager.get_all_mappings()
        
        # 获取统计信息
        stats = mapping_manager.get_mapping_stats()
        
        return jsonify({
            'success': True,
            'data': {
                'mappings': mappings,
                'stats': stats
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取用户ID映射失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@user_memory_admin_bp.route('/test-user-memory', methods=['POST'])
def test_user_memory():
    """测试用户记忆功能"""
    try:
        data = request.get_json()
        test_user_id = data.get('user_id', 'test-user-12345')
        test_message = data.get('message', '我喜欢看电影，特别是科幻片')
        
        # 初始化记忆管理器
        memory_manager = MemobaseMemoryManager()
        
        # 提取记忆
        memories = memory_manager.extract_memories_from_text(test_user_id, test_message)
        
        # 获取相关记忆
        relevant_memories = memory_manager.get_relevant_memories(test_user_id, test_message, limit=5)
        
        # 获取用户上下文
        user_context = memory_manager.get_user_context(test_user_id, max_token_size=300)
        
        # 获取映射信息
        mapping_manager = get_user_id_mapping_manager()
        memobase_uuid = mapping_manager.get_memobase_uuid(test_user_id)
        
        return jsonify({
            'success': True,
            'data': {
                'test_user_id': test_user_id,
                'memobase_uuid': memobase_uuid,
                'test_message': test_message,
                'extracted_memories': memories,
                'relevant_memories': relevant_memories,
                'user_context': user_context,
                'context_length': len(user_context)
            }
        })
        
    except Exception as e:
        logger.error(f"💥 测试用户记忆失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



