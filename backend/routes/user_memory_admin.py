"""
用户记忆管理路由
提供用户记忆管理的管理接口
"""

import logging
from flask import Blueprint, jsonify, request, render_template_string
from services.memobase_memory_manager import MemobaseMemoryManager
from services.user_id_mapping_manager import get_user_id_mapping_manager

logger = logging.getLogger(__name__)

# 创建蓝图
user_memory_admin_bp = Blueprint('user_memory_admin', __name__, url_prefix='/admin/user-memory')


@user_memory_admin_bp.route('/mappings')
def get_user_mappings():
    """获取用户ID映射信息"""
    try:
        mapping_manager = get_user_id_mapping_manager()
        
        # 获取所有映射
        mappings = mapping_manager.get_all_mappings()
        
        # 获取统计信息
        stats = mapping_manager.get_mapping_stats()
        
        return jsonify({
            'success': True,
            'data': {
                'mappings': mappings,
                'stats': stats
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取用户ID映射失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@user_memory_admin_bp.route('/test-user-memory', methods=['POST'])
def test_user_memory():
    """测试用户记忆功能"""
    try:
        data = request.get_json()
        test_user_id = data.get('user_id', 'test-user-12345')
        test_message = data.get('message', '我喜欢看电影，特别是科幻片')
        
        # 初始化记忆管理器
        memory_manager = MemobaseMemoryManager()
        
        # 提取记忆
        memories = memory_manager.extract_memories_from_text(test_user_id, test_message)
        
        # 获取相关记忆
        relevant_memories = memory_manager.get_relevant_memories(test_user_id, test_message, limit=5)
        
        # 获取用户上下文
        user_context = memory_manager.get_user_context(test_user_id, max_token_size=300)
        
        # 获取映射信息
        mapping_manager = get_user_id_mapping_manager()
        memobase_uuid = mapping_manager.get_memobase_uuid(test_user_id)
        
        return jsonify({
            'success': True,
            'data': {
                'test_user_id': test_user_id,
                'memobase_uuid': memobase_uuid,
                'test_message': test_message,
                'extracted_memories': memories,
                'relevant_memories': relevant_memories,
                'user_context': user_context,
                'context_length': len(user_context)
            }
        })
        
    except Exception as e:
        logger.error(f"💥 测试用户记忆失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@user_memory_admin_bp.route('/dashboard')
def user_memory_dashboard():
    """用户记忆管理仪表板"""
    dashboard_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户记忆管理仪表板</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 28px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            font-size: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input, .input-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .input-group textarea {
            height: 80px;
            resize: vertical;
        }
        .loading {
            color: #6c757d;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 用户记忆管理仪表板</h1>
            <p>管理和测试用户记忆功能，包括ID映射和记忆提取</p>
        </div>

        <div class="section">
            <h2>📋 用户ID映射管理</h2>
            <p>查看用户ID与Memobase UUID之间的映射关系</p>
            <button class="btn" onclick="loadUserMappings()">加载用户映射</button>
            <div id="mappings-result" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>🧪 用户记忆测试</h2>
            <p>测试用户记忆提取和检索功能</p>
            
            <div class="input-group">
                <label for="test-user-id">测试用户ID:</label>
                <input type="text" id="test-user-id" value="test-user-12345" placeholder="输入用户ID">
            </div>
            
            <div class="input-group">
                <label for="test-message">测试消息:</label>
                <textarea id="test-message" placeholder="输入要提取记忆的消息">我喜欢看电影，特别是科幻片。我最近在学习编程，希望能成为一名软件工程师。</textarea>
            </div>
            
            <button class="btn" onclick="testUserMemory()">测试用户记忆</button>
            <div id="memory-test-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        async function loadUserMappings() {
            const resultDiv = document.getElementById('mappings-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '加载中...';
            
            try {
                const response = await fetch('/admin/user-memory/mappings');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.textContent = JSON.stringify(data.data, null, 2);
                } else {
                    resultDiv.innerHTML = `<div class="error">错误: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            }
        }
        
        async function testUserMemory() {
            const resultDiv = document.getElementById('memory-test-result');
            const userId = document.getElementById('test-user-id').value;
            const message = document.getElementById('test-message').value;
            
            if (!userId || !message) {
                alert('请输入用户ID和测试消息');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试中...';
            
            try {
                const response = await fetch('/admin/user-memory/test-user-memory', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        message: message
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.textContent = JSON.stringify(data.data, null, 2);
                } else {
                    resultDiv.innerHTML = `<div class="error">错误: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
    """
    return render_template_string(dashboard_html)
