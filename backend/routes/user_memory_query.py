"""
用户记忆查询路由
提供查询chat.db中每个用户记忆的功能
"""

import logging
from flask import Blueprint, jsonify, request
from services.memobase_memory_manager import MemobaseMemoryManager
from models.database import DatabaseManager

logger = logging.getLogger(__name__)

# 创建蓝图
user_memory_query_bp = Blueprint('user_memory_query', __name__, url_prefix='/admin/user-memory-query')


@user_memory_query_bp.route('/users')
def get_users_with_memories():
    """获取有记忆的用户列表"""
    try:
        db = DatabaseManager()
        users = db.get_users_with_memories()
        
        return jsonify({
            'success': True,
            'data': {
                'users': users,
                'total': len(users)
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取用户列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@user_memory_query_bp.route('/user/<user_id>/memories')
def get_user_memories(user_id):
    """获取指定用户的记忆"""
    try:
        limit = int(request.args.get('limit', 20))
        query = request.args.get('query', '')
        
        # 初始化记忆管理器
        memory_manager = MemobaseMemoryManager()
        
        # 获取用户记忆
        if query:
            memories = memory_manager.get_relevant_memories(user_id, query, limit=limit)
        else:
            memories = memory_manager.get_relevant_memories(user_id, limit=limit)
        
        # 获取用户上下文
        user_context = memory_manager.get_user_context(user_id, max_token_size=500)
        
        # 获取用户基本信息
        db = DatabaseManager()
        user_info = db.get_user_by_id(user_id)
        memobase_uuid = db.get_memobase_uuid(user_id)
        
        return jsonify({
            'success': True,
            'data': {
                'user_id': user_id,
                'user_info': user_info,
                'memobase_uuid': memobase_uuid,
                'memories': memories,
                'user_context': user_context,
                'context_length': len(user_context),
                'memory_count': len(memories)
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取用户记忆失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@user_memory_query_bp.route('/user/<user_id>/conversations')
def get_user_conversations(user_id):
    """获取用户的对话记录"""
    try:
        limit = int(request.args.get('limit', 50))
        
        db = DatabaseManager()
        conversations = db.get_recent_conversations(user_id, limit=limit)
        
        return jsonify({
            'success': True,
            'data': {
                'user_id': user_id,
                'conversations': conversations,
                'total': len(conversations)
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取用户对话记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



