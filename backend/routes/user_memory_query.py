"""
用户记忆查询路由
提供查询chat.db中每个用户记忆的功能
"""

import logging
from flask import Blueprint, jsonify, request, render_template_string
from services.memobase_memory_manager import MemobaseMemoryManager
from models.database import DatabaseManager

logger = logging.getLogger(__name__)

# 创建蓝图
user_memory_query_bp = Blueprint('user_memory_query', __name__, url_prefix='/admin/user-memory-query')


@user_memory_query_bp.route('/users')
def get_users_with_memories():
    """获取有记忆的用户列表"""
    try:
        db = DatabaseManager()
        users = db.get_users_with_memories()
        
        return jsonify({
            'success': True,
            'data': {
                'users': users,
                'total': len(users)
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取用户列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@user_memory_query_bp.route('/user/<user_id>/memories')
def get_user_memories(user_id):
    """获取指定用户的记忆"""
    try:
        limit = int(request.args.get('limit', 20))
        query = request.args.get('query', '')
        
        # 初始化记忆管理器
        memory_manager = MemobaseMemoryManager()
        
        # 获取用户记忆
        if query:
            memories = memory_manager.get_relevant_memories(user_id, query, limit=limit)
        else:
            memories = memory_manager.get_relevant_memories(user_id, limit=limit)
        
        # 获取用户上下文
        user_context = memory_manager.get_user_context(user_id, max_token_size=500)
        
        # 获取用户基本信息
        db = DatabaseManager()
        user_info = db.get_user_by_id(user_id)
        memobase_uuid = db.get_memobase_uuid(user_id)
        
        return jsonify({
            'success': True,
            'data': {
                'user_id': user_id,
                'user_info': user_info,
                'memobase_uuid': memobase_uuid,
                'memories': memories,
                'user_context': user_context,
                'context_length': len(user_context),
                'memory_count': len(memories)
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取用户记忆失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@user_memory_query_bp.route('/user/<user_id>/conversations')
def get_user_conversations(user_id):
    """获取用户的对话记录"""
    try:
        limit = int(request.args.get('limit', 50))
        
        db = DatabaseManager()
        conversations = db.get_recent_conversations(user_id, limit=limit)
        
        return jsonify({
            'success': True,
            'data': {
                'user_id': user_id,
                'conversations': conversations,
                'total': len(conversations)
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取用户对话记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@user_memory_query_bp.route('/dashboard')
def user_memory_query_dashboard():
    """用户记忆查询仪表板"""
    dashboard_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户记忆查询仪表板</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 28px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            font-size: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 500px;
            overflow-y: auto;
        }
        .user-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .user-card {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        .user-card:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.2);
        }
        .user-card.selected {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        .user-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .user-card .meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .loading {
            color: #6c757d;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }
        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 用户记忆查询仪表板</h1>
            <p>查询chat.db中每个用户的记忆和对话记录</p>
        </div>

        <div class="section">
            <h2>👥 用户列表</h2>
            <p>显示所有有记忆数据的用户</p>
            <button class="btn" onclick="loadUsers()">加载用户列表</button>
            <div id="users-list" class="user-list" style="display: none;"></div>
        </div>

        <div class="section" id="user-details" style="display: none;">
            <h2>📋 用户详情</h2>
            <div id="selected-user-info"></div>
            
            <div class="tabs">
                <div class="tab active" onclick="switchTab('memories')">记忆数据</div>
                <div class="tab" onclick="switchTab('conversations')">对话记录</div>
            </div>
            
            <div id="memories-tab" class="tab-content active">
                <div class="input-group">
                    <label for="memory-query">搜索记忆:</label>
                    <input type="text" id="memory-query" placeholder="输入关键词搜索记忆">
                </div>
                <button class="btn btn-success" onclick="loadUserMemories()">查询记忆</button>
                <div id="memories-result" class="result" style="display: none;"></div>
            </div>
            
            <div id="conversations-tab" class="tab-content">
                <button class="btn btn-success" onclick="loadUserConversations()">查询对话记录</button>
                <div id="conversations-result" class="result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        let selectedUserId = null;
        
        async function loadUsers() {
            const listDiv = document.getElementById('users-list');
            listDiv.style.display = 'block';
            listDiv.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await fetch('/admin/user-memory-query/users');
                const data = await response.json();
                
                if (data.success) {
                    const users = data.data.users;
                    if (users.length === 0) {
                        listDiv.innerHTML = '<div class="error">暂无用户记忆数据</div>';
                        return;
                    }
                    
                    listDiv.innerHTML = users.map(user => `
                        <div class="user-card" onclick="selectUser('${user.user_id}', '${user.username || '未知'}', '${user.nickname || ''}')">
                            <h4>${user.nickname || user.username || user.user_id}</h4>
                            <div class="meta">用户ID: ${user.user_id}</div>
                            <div class="meta">Memobase UUID: ${user.memobase_uuid}</div>
                            <div class="meta">对话数量: ${user.conversation_count}</div>
                            <div class="meta">最后对话: ${user.last_conversation || '无'}</div>
                            <div class="meta">创建时间: ${user.created_at}</div>
                        </div>
                    `).join('');
                } else {
                    listDiv.innerHTML = `<div class="error">错误: ${data.error}</div>`;
                }
            } catch (error) {
                listDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            }
        }
        
        function selectUser(userId, username, nickname) {
            selectedUserId = userId;
            
            // 更新选中状态
            document.querySelectorAll('.user-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            
            // 显示用户详情区域
            const detailsDiv = document.getElementById('user-details');
            detailsDiv.style.display = 'block';
            
            // 更新用户信息
            const userInfoDiv = document.getElementById('selected-user-info');
            userInfoDiv.innerHTML = `
                <div class="success">
                    已选择用户: <strong>${nickname || username || userId}</strong> (ID: ${userId})
                </div>
            `;
            
            // 清空之前的结果
            document.getElementById('memories-result').style.display = 'none';
            document.getElementById('conversations-result').style.display = 'none';
        }
        
        async function loadUserMemories() {
            if (!selectedUserId) {
                alert('请先选择一个用户');
                return;
            }
            
            const resultDiv = document.getElementById('memories-result');
            const query = document.getElementById('memory-query').value;
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = '查询中...';
            
            try {
                const url = `/admin/user-memory-query/user/${selectedUserId}/memories?limit=20${query ? '&query=' + encodeURIComponent(query) : ''}`;
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.textContent = JSON.stringify(data.data, null, 2);
                } else {
                    resultDiv.innerHTML = `<div class="error">错误: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            }
        }
        
        async function loadUserConversations() {
            if (!selectedUserId) {
                alert('请先选择一个用户');
                return;
            }
            
            const resultDiv = document.getElementById('conversations-result');
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = '查询中...';
            
            try {
                const response = await fetch(`/admin/user-memory-query/user/${selectedUserId}/conversations?limit=50`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.textContent = JSON.stringify(data.data, null, 2);
                } else {
                    resultDiv.innerHTML = `<div class="error">错误: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            }
        }
        
        function switchTab(tabName) {
            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
            
            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName + '-tab').classList.add('active');
        }
    </script>
</body>
</html>
    """
    return render_template_string(dashboard_html)
