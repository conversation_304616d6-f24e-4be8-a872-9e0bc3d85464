"""
配置展示路由
展示丰富后的人物配置和事件配置
"""

import logging
from flask import Blueprint, jsonify, render_template_string
from config import Config

logger = logging.getLogger(__name__)

# 创建蓝图
config_display_bp = Blueprint('config_display', __name__, url_prefix='/admin/config')


@config_display_bp.route('/persona')
def get_persona_config():
    """获取虚拟人配置"""
    try:
        return jsonify({
            'success': True,
            'data': {
                'persona_config': Config.PERSONA_CONFIG,
                'affection_config': Config.AFFECTION_CONFIG,
                'time_awareness_config': Config.TIME_AWARENESS_CONFIG,
                'event_memory_config': Config.EVENT_MEMORY_CONFIG
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取虚拟人配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_display_bp.route('/dashboard')
def config_dashboard():
    """配置展示仪表板"""
    dashboard_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟人配置展示仪表板</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 32px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .header p {
            color: #666;
            font-size: 16px;
            margin-top: 10px;
        }
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            font-size: 24px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .section h2::before {
            content: attr(data-icon);
            margin-right: 10px;
            font-size: 28px;
        }
        .btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 15px;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .result {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            max-height: 600px;
            overflow-y: auto;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }
        .loading {
            color: #6c757d;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
        }
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .config-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .config-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .config-card h3 {
            margin-top: 0;
            color: #495057;
            font-size: 18px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 10px;
        }
        .highlight {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌸 沈沐心（沐沐）虚拟人配置展示</h1>
            <p>25岁美术学院学生 | ENFJ人格 | 心理咨询师虚拟人恋爱陪伴系统</p>
        </div>

        <div class="section">
            <h2 data-icon="👤">基础人物配置</h2>
            <p>展示沈沐心的基本信息、性格特征、兴趣爱好等核心配置</p>
            <button class="btn" onclick="loadPersonaConfig()">查看人物配置</button>
            <div id="persona-result" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h2 data-icon="💕">恋爱系统配置</h2>
            <p>展示好感度系统、恋爱特征、关系里程碑等恋爱陪伴相关配置</p>
            <button class="btn" onclick="loadAffectionConfig()">查看恋爱配置</button>
            <div id="affection-result" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h2 data-icon="⏰">时间感知配置</h2>
            <p>展示虚拟人的时间概念、日常作息、季节性情绪等时间相关配置</p>
            <button class="btn" onclick="loadTimeConfig()">查看时间配置</button>
            <div id="time-result" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h2 data-icon="📝">事件记忆配置</h2>
            <p>展示重要事件分类、记忆触发器、保留策略等事件记忆相关配置</p>
            <button class="btn" onclick="loadEventConfig()">查看事件配置</button>
            <div id="event-result" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h2 data-icon="🎯">完整配置概览</h2>
            <p>一次性查看所有配置信息的完整概览</p>
            <button class="btn" onclick="loadAllConfigs()">查看完整配置</button>
            <div id="all-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let allConfigData = null;
        
        async function loadPersonaConfig() {
            await loadConfigData();
            if (allConfigData) {
                const resultDiv = document.getElementById('persona-result');
                resultDiv.style.display = 'block';
                resultDiv.textContent = JSON.stringify(allConfigData.persona_config, null, 2);
            }
        }
        
        async function loadAffectionConfig() {
            await loadConfigData();
            if (allConfigData) {
                const resultDiv = document.getElementById('affection-result');
                resultDiv.style.display = 'block';
                resultDiv.textContent = JSON.stringify(allConfigData.affection_config, null, 2);
            }
        }
        
        async function loadTimeConfig() {
            await loadConfigData();
            if (allConfigData) {
                const resultDiv = document.getElementById('time-result');
                resultDiv.style.display = 'block';
                resultDiv.textContent = JSON.stringify(allConfigData.time_awareness_config, null, 2);
            }
        }
        
        async function loadEventConfig() {
            await loadConfigData();
            if (allConfigData) {
                const resultDiv = document.getElementById('event-result');
                resultDiv.style.display = 'block';
                resultDiv.textContent = JSON.stringify(allConfigData.event_memory_config, null, 2);
            }
        }
        
        async function loadAllConfigs() {
            await loadConfigData();
            if (allConfigData) {
                const resultDiv = document.getElementById('all-result');
                resultDiv.style.display = 'block';
                resultDiv.textContent = JSON.stringify(allConfigData, null, 2);
            }
        }
        
        async function loadConfigData() {
            if (allConfigData) return; // 已经加载过了
            
            try {
                const response = await fetch('/admin/config/persona');
                const data = await response.json();
                
                if (data.success) {
                    allConfigData = data.data;
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                console.error('加载配置失败:', error);
                alert('加载配置失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
    """
    return render_template_string(dashboard_html)
