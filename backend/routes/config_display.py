"""
配置展示路由
展示丰富后的人物配置和事件配置
"""

import logging
from flask import Blueprint, jsonify
from config import Config

logger = logging.getLogger(__name__)

# 创建蓝图
config_display_bp = Blueprint('config_display', __name__, url_prefix='/admin/config')


@config_display_bp.route('/persona')
def get_persona_config():
    """获取虚拟人配置"""
    try:
        return jsonify({
            'success': True,
            'data': {
                'persona_config': Config.PERSONA_CONFIG,
                'affection_config': Config.AFFECTION_CONFIG,
                'time_awareness_config': Config.TIME_AWARENESS_CONFIG,
                'event_memory_config': Config.EVENT_MEMORY_CONFIG
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取虚拟人配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



