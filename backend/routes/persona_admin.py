"""
虚拟人管理路由
提供虚拟人ID和记忆管理的管理接口
"""

import logging
from flask import Blueprint, jsonify, request
from services.memobase_persona_memory_manager import MemobasePersonaMemoryManager
from services.persona_id_manager import get_persona_id_manager

logger = logging.getLogger(__name__)

# 创建蓝图
persona_admin_bp = Blueprint('persona_admin', __name__, url_prefix='/admin/persona')


@persona_admin_bp.route('/id-info')
def get_persona_id_info():
    """获取虚拟人ID信息"""
    try:
        # 获取虚拟人记忆管理器
        memory_manager = MemobasePersonaMemoryManager()
        
        # 获取ID信息
        id_info = memory_manager.get_persona_id_info()
        
        # 获取记忆统计
        memory_stats = memory_manager.get_memory_stats()
        
        # 获取token使用统计
        token_stats = memory_manager.get_token_usage_stats()

        return jsonify({
            'success': True,
            'data': {
                'id_info': id_info,
                'memory_stats': memory_stats,
                'token_stats': token_stats
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取虚拟人ID信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@persona_admin_bp.route('/regenerate-id', methods=['POST'])
def regenerate_persona_id():
    """重新生成虚拟人ID（危险操作）"""
    try:
        # 获取确认参数
        confirm = request.json.get('confirm', False) if request.is_json else False
        
        if not confirm:
            return jsonify({
                'success': False,
                'error': '需要确认参数才能执行此危险操作'
            }), 400
        
        # 获取ID管理器
        id_manager = get_persona_id_manager()
        
        # 重新生成ID
        new_id = id_manager.regenerate_persona_id()
        
        logger.warning(f"⚠️ 虚拟人ID已重新生成: {new_id}")
        
        return jsonify({
            'success': True,
            'data': {
                'new_persona_id': new_id,
                'warning': '虚拟人ID已重新生成，历史记忆数据可能无法关联'
            }
        })
        
    except Exception as e:
        logger.error(f"💥 重新生成虚拟人ID失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500




