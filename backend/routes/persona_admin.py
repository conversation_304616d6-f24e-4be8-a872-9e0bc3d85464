"""
虚拟人管理路由
提供虚拟人ID和记忆管理的管理接口
"""

import logging
from flask import Blueprint, jsonify, request, render_template_string
from services.memobase_persona_memory_manager import MemobasePersonaMemoryManager
from services.persona_id_manager import get_persona_id_manager

logger = logging.getLogger(__name__)

# 创建蓝图
persona_admin_bp = Blueprint('persona_admin', __name__, url_prefix='/admin/persona')


@persona_admin_bp.route('/id-info')
def get_persona_id_info():
    """获取虚拟人ID信息"""
    try:
        # 获取虚拟人记忆管理器
        memory_manager = MemobasePersonaMemoryManager()
        
        # 获取ID信息
        id_info = memory_manager.get_persona_id_info()
        
        # 获取记忆统计
        memory_stats = memory_manager.get_memory_stats()
        
        # 获取token使用统计
        token_stats = memory_manager.get_token_usage_stats()

        return jsonify({
            'success': True,
            'data': {
                'id_info': id_info,
                'memory_stats': memory_stats,
                'token_stats': token_stats
            }
        })
        
    except Exception as e:
        logger.error(f"💥 获取虚拟人ID信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@persona_admin_bp.route('/regenerate-id', methods=['POST'])
def regenerate_persona_id():
    """重新生成虚拟人ID（危险操作）"""
    try:
        # 获取确认参数
        confirm = request.json.get('confirm', False) if request.is_json else False
        
        if not confirm:
            return jsonify({
                'success': False,
                'error': '需要确认参数才能执行此危险操作'
            }), 400
        
        # 获取ID管理器
        id_manager = get_persona_id_manager()
        
        # 重新生成ID
        new_id = id_manager.regenerate_persona_id()
        
        logger.warning(f"⚠️ 虚拟人ID已重新生成: {new_id}")
        
        return jsonify({
            'success': True,
            'data': {
                'new_persona_id': new_id,
                'warning': '虚拟人ID已重新生成，历史记忆数据可能无法关联'
            }
        })
        
    except Exception as e:
        logger.error(f"💥 重新生成虚拟人ID失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@persona_admin_bp.route('/dashboard')
def persona_dashboard():
    """虚拟人管理仪表板"""
    dashboard_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟人管理仪表板</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 28px;
        }
        .header p {
            margin: 10px 0 0 0;
            color: #666;
            font-size: 16px;
        }
        .card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        .info-value {
            color: #6c757d;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            word-break: break-all;
        }
        .btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 15px;
        }
        .btn:hover {
            background: #c82333;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 虚拟人管理仪表板</h1>
            <p>管理虚拟人ID和记忆系统</p>
        </div>

        <div id="loading" class="loading">
            <p>正在加载虚拟人信息...</p>
        </div>

        <div id="content" style="display: none;">
            <div class="card">
                <h3>📋 虚拟人ID信息</h3>
                <div id="id-info" class="info-grid"></div>
            </div>

            <div class="card">
                <h3>📊 记忆统计</h3>
                <div id="memory-stats" class="info-grid"></div>
            </div>

            <div class="card">
                <h3>🔥 Token使用统计</h3>
                <div id="token-stats" class="info-grid"></div>
            </div>

            <div class="card">
                <h3>⚠️ 危险操作</h3>
                <div class="warning">
                    <strong>警告：</strong>重新生成虚拟人ID将导致历史记忆数据无法关联，请谨慎操作！
                </div>
                <button id="regenerate-btn" class="btn" onclick="regenerateId()">
                    重新生成虚拟人ID
                </button>
            </div>
        </div>

        <div id="error" style="display: none;" class="error"></div>
    </div>

    <script>
        // 加载虚拟人信息
        async function loadPersonaInfo() {
            try {
                const response = await fetch('/admin/persona/id-info');
                const result = await response.json();
                
                if (result.success) {
                    displayPersonaInfo(result.data);
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('content').style.display = 'block';
                } else {
                    showError('加载失败: ' + result.error);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 显示虚拟人信息
        function displayPersonaInfo(data) {
            // 显示ID信息
            const idInfo = data.id_info;
            const idInfoHtml = Object.entries(idInfo).map(([key, value]) => {
                const label = getFieldLabel(key);
                return `
                    <div class="info-item">
                        <div class="info-label">${label}</div>
                        <div class="info-value">${value}</div>
                    </div>
                `;
            }).join('');
            document.getElementById('id-info').innerHTML = idInfoHtml;

            // 显示记忆统计
            const memoryStats = data.memory_stats;
            const memoryStatsHtml = Object.entries(memoryStats).map(([key, value]) => {
                const label = getFieldLabel(key);
                return `
                    <div class="info-item">
                        <div class="info-label">${label}</div>
                        <div class="info-value">${value}</div>
                    </div>
                `;
            }).join('');
            document.getElementById('memory-stats').innerHTML = memoryStatsHtml;

            // 显示token统计
            const tokenStats = data.token_stats || {};
            const tokenStatsHtml = Object.entries(tokenStats).map(([key, value]) => {
                const label = getFieldLabel(key);
                return `
                    <div class="info-item">
                        <div class="info-label">${label}</div>
                        <div class="info-value">${value}</div>
                    </div>
                `;
            }).join('');
            document.getElementById('token-stats').innerHTML = tokenStatsHtml;
        }

        // 获取字段标签
        function getFieldLabel(key) {
            const labels = {
                'persona_id': '虚拟人ID',
                'current_persona_id': '当前虚拟人ID',
                'created_at': '创建时间',
                'persona_name': '虚拟人姓名',
                'persona_nickname': '虚拟人昵称',
                'project_instance_id': '项目实例ID',
                'version': '版本',
                'total_memories': '记忆总数',
                'provider': '记忆提供商',
                'last_updated': '最后更新时间',
                'total_tokens': '总Token使用量',
                'monthly_tokens': '月度Token使用量',
                'daily_tokens': '日Token使用量',
                'context_tokens': '上下文Token使用量',
                'profile_tokens': 'Profile Token使用量',
                'event_tokens': 'Event Token使用量'
            };
            return labels[key] || key;
        }

        // 重新生成ID
        async function regenerateId() {
            if (!confirm('确定要重新生成虚拟人ID吗？这将导致历史记忆数据无法关联！')) {
                return;
            }

            const btn = document.getElementById('regenerate-btn');
            btn.disabled = true;
            btn.textContent = '正在重新生成...';

            try {
                const response = await fetch('/admin/persona/regenerate-id', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ confirm: true })
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('虚拟人ID重新生成成功！\\n新ID: ' + result.data.new_persona_id);
                    location.reload();
                } else {
                    alert('重新生成失败: ' + result.error);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            } finally {
                btn.disabled = false;
                btn.textContent = '重新生成虚拟人ID';
            }
        }

        // 显示错误
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').textContent = message;
        }

        // 页面加载时获取信息
        window.onload = loadPersonaInfo;
    </script>
</body>
</html>
    """
    return render_template_string(dashboard_html)
