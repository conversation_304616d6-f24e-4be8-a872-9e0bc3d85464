"""
基于Memobase的虚拟人个人记忆管理模块
管理虚拟人的个人背景、经历和记忆
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional, Any
from clients.memobase_client import get_memobase_client
from config import Config
from services.persona_id_manager import get_persona_id_manager

# 配置日志
logger = logging.getLogger(__name__)


class MemobasePersonaMemoryManager:
    """基于Memobase的虚拟人个人记忆管理器"""

    def __init__(self, llm_service=None):
        """
        初始化Memobase个人记忆管理器

        Args:
            llm_service: LLM服务实例（保留用于兼容性）
        """
        self.llm_service = llm_service
        self.config = Config.MEMOBASE_CONFIG
        self.persona_config = Config.PERSONA_CONFIG

        # 获取项目唯一的虚拟人ID
        self.persona_id_manager = get_persona_id_manager()
        self.persona_user_id = self.persona_id_manager.get_persona_id()
        
        # 初始化Memobase客户端
        try:
            self.memobase_client = get_memobase_client()
            logger.info(f"✅ Memobase个人记忆管理器初始化成功 - 虚拟人ID: {self.persona_user_id}")
        except Exception as e:
            logger.error(f"❌ Memobase个人记忆管理器初始化失败: {e}")
            raise

        # 确保虚拟人记忆存在
        self._ensure_persona_memories_exist()

    def _ensure_persona_memories_exist(self):
        """确保虚拟人个人记忆存在"""
        try:
            # 获取或创建虚拟人用户
            persona_user = self.memobase_client.get_or_create_user(
                self.persona_user_id,
                {
                    "name": self.persona_config['name'],
                    "nickname": self.persona_config['nickname'],
                    "type": "persona",
                    "created_at": datetime.now().isoformat()
                }
            )
            
            # 检查是否已有记忆
            profile = self.memobase_client.get_user_profile(persona_user)
            if profile:
                logger.info("✅ 虚拟人个人记忆已存在，跳过初始化")
                return
            
            logger.info("🔄 开始初始化虚拟人个人记忆...")
            self._initialize_persona_memories(persona_user)
            
        except Exception as e:
            logger.error(f"❌ 检查虚拟人记忆失败: {e}")

    def _initialize_persona_memories(self, persona_user):
        """初始化虚拟人个人记忆"""
        try:
            # 基本信息记忆
            basic_memories = [
                f"我叫{self.persona_config['name']}，大家都叫我{self.persona_config['nickname']}",
                f"我今年{self.persona_config['age']}岁，是个{self.persona_config['gender']}生",
                f"我是{self.persona_config['profession']}",
                f"我住在{self.persona_config['location']}",
                f"我的性格是{self.persona_config['personality']}",
                f"我说话的风格是{self.persona_config['speaking_style']}"
            ]
            
            # 其他信息记忆
            for info in self.persona_config['other_infos']:
                basic_memories.append(f"关于我：{info}")
            
            # 爱好记忆
            for hobby in self.persona_config['hobbies']:
                basic_memories.append(f"我的爱好：{hobby}")
            
            # 说话示例记忆
            for example in self.persona_config['speaking_examples']:
                basic_memories.append(f"我的说话习惯：{example}")
            
            # 优化：分批插入记忆，减少单次token消耗
            batch_size = 5  # 每批处理5条记忆
            total_inserted = 0

            for i in range(0, len(basic_memories), batch_size):
                batch_memories = basic_memories[i:i + batch_size]
                batch_messages = []

                for j, memory in enumerate(batch_memories):
                    batch_messages.extend([
                        {
                            "role": "assistant",
                            "content": f"记忆初始化批次 {i//batch_size + 1} - 条目 {j+1}: {memory}",
                            "timestamp": datetime.now().isoformat()
                        }
                    ])

                # 插入当前批次到Memobase
                blob_id = self.memobase_client.insert_chat_data(persona_user, batch_messages)
                total_inserted += len(batch_memories)

                logger.debug(f"📦 已插入第 {i//batch_size + 1} 批记忆，共 {len(batch_memories)} 条")

            logger.info(f"📦 分批插入完成，总计 {total_inserted} 条记忆")
            
            # 刷新记忆
            self.memobase_client.flush_user_memory(persona_user)
            
            logger.info(f"✅ 初始化了 {total_inserted} 条虚拟人个人记忆")
            
        except Exception as e:
            logger.error(f"❌ 初始化虚拟人记忆失败: {e}")

    def add_persona_memory(self, memory_type: str, category: str, title: str, content: str,
                          emotion: str = None, importance: float = 1.0, keywords: str = None,
                          time_period: str = 'recent', sharing_level: int = 1) -> str:
        """
        添加个人记忆到Memobase

        Args:
            memory_type: 记忆类型
            category: 分类
            title: 标题
            content: 内容
            emotion: 情感
            importance: 重要性
            keywords: 关键词
            time_period: 时间段
            sharing_level: 分享级别

        Returns:
            记忆ID
        """
        try:
            # 获取虚拟人用户
            persona_user = self.memobase_client.get_or_create_user(self.persona_user_id)
            
            # 构造记忆消息
            messages = [
                {
                    "role": "assistant",
                    "content": f"记忆类型: {memory_type}, 分类: {category}, 标题: {title}, 情感: {emotion}, 重要性: {importance}, 时间段: {time_period}, 分享级别: {sharing_level}"
                },
                {
                    "role": "assistant",
                    "content": content,
                    "timestamp": datetime.now().isoformat()
                }
            ]
            
            # 插入到Memobase
            blob_id = self.memobase_client.insert_chat_data(persona_user, messages)
            
            # 如果配置了自动刷新，立即刷新记忆
            if self.config.get('auto_flush', True):
                self.memobase_client.flush_user_memory(persona_user)
            
            logger.info(f"💾 虚拟人记忆已保存 - 类型: {memory_type}, 标题: {title}")
            return blob_id
            
        except Exception as e:
            logger.error(f"💥 添加虚拟人记忆失败: {e}")
            return ""

    def get_relevant_persona_memories(self, context: str, user_affection: int = 50,
                                    limit: int = 3) -> List[Dict]:
        """
        根据对话上下文获取相关的个人记忆

        Args:
            context: 对话上下文
            user_affection: 用户好感度，影响分享私密记忆的概率
            limit: 返回记忆数量限制

        Returns:
            相关记忆列表
        """
        try:
            # 获取虚拟人用户
            persona_user = self.memobase_client.get_or_create_user(self.persona_user_id)
            
            # 获取用户画像作为记忆
            profile = self.memobase_client.get_user_profile(persona_user, need_json=False)
            
            # 转换为兼容格式
            memories = []
            for item in profile:
                if hasattr(item, 'topic') and hasattr(item, 'content'):
                    # 根据好感度过滤私密记忆
                    sharing_level = 1  # 默认公开级别
                    if user_affection < 30:
                        sharing_level = 1  # 只分享公开记忆
                    elif user_affection < 70:
                        sharing_level = 2  # 分享一般记忆
                    else:
                        sharing_level = 3  # 分享私密记忆
                    
                    memories.append({
                        'content': item.content,
                        'type': item.topic,
                        'category': getattr(item, 'sub_topic', ''),
                        'title': f"{item.topic}记忆",
                        'emotion': 'neutral',
                        'importance': 2.0,
                        'time_period': 'recent',
                        'sharing_level': sharing_level,
                        'created_at': getattr(item, 'created_at', datetime.now().isoformat())
                    })
            
            # 根据好感度和重要性排序
            memories.sort(key=lambda x: (x['sharing_level'] <= sharing_level, x['importance']), reverse=True)
            
            # 应用限制
            memories = memories[:limit]
            
            logger.info(f"🔍 获取虚拟人记忆 - 上下文: {context[:30]}..., 好感度: {user_affection}, 结果: {len(memories)} 条")
            return memories
            
        except Exception as e:
            logger.error(f"💥 获取虚拟人记忆失败: {e}")
            return []

    def get_persona_context(self, max_token_size: int = None, prefer_topics: List[str] = None,
                           chats: List[Dict] = None, optimize_tokens: bool = True) -> str:
        """
        获取虚拟人上下文（用于系统提示词）

        Args:
            max_token_size: 最大token数
            prefer_topics: 优先话题
            chats: 聊天历史（用于上下文相关性）
            optimize_tokens: 是否优化token使用

        Returns:
            格式化的虚拟人上下文字符串
        """
        try:
            # 获取虚拟人用户
            persona_user = self.memobase_client.get_or_create_user(self.persona_user_id)

            # 使用配置的默认值，但支持token优化
            default_token_size = self.config.get('max_token_size', 500)
            if optimize_tokens:
                # 根据对话长度动态调整token大小
                if chats and len(chats) > 10:
                    default_token_size = min(default_token_size, 300)
                elif chats and len(chats) > 5:
                    default_token_size = min(default_token_size, 400)

            max_token_size = max_token_size or default_token_size
            prefer_topics = prefer_topics or ['basic_info', 'personality', 'hobbies', 'speaking_style']

            # 获取Memobase生成的上下文，传递聊天历史以提高相关性
            context = self.memobase_client.get_user_context(
                persona_user,
                max_token_size=max_token_size,
                prefer_topics=prefer_topics,
                chats=chats,
                optimize_tokens=optimize_tokens
            )

            logger.debug(f"📄 获取虚拟人上下文 - 长度: {len(context)}, Token限制: {max_token_size}")
            return context

        except Exception as e:
            logger.error(f"💥 获取虚拟人上下文失败: {e}")
            return ""

    def get_memory_stats(self) -> Dict:
        """
        获取虚拟人记忆统计信息

        Returns:
            记忆统计信息
        """
        try:
            # 获取虚拟人用户
            persona_user = self.memobase_client.get_or_create_user(self.persona_user_id)
            
            # 获取用户画像
            profile = self.memobase_client.get_user_profile(persona_user, need_json=False)
            
            # 统计信息
            total_memories = len(profile) if profile else 0
            
            return {
                'total_memories': total_memories,
                'persona_name': self.persona_config['name'],
                'provider': 'memobase',
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"💥 获取虚拟人记忆统计失败: {e}")
            return {
                'total_memories': 0,
                'persona_name': self.persona_config['name'],
                'provider': 'memobase',
                'error': str(e)
            }

    def get_persona_id_info(self) -> Dict:
        """
        获取虚拟人ID的详细信息

        Returns:
            虚拟人ID的详细信息
        """
        try:
            id_info = self.persona_id_manager.get_id_info()
            id_info['current_persona_id'] = self.persona_user_id
            return id_info
        except Exception as e:
            logger.error(f"💥 获取虚拟人ID信息失败: {e}")
            return {
                'current_persona_id': self.persona_user_id,
                'error': str(e)
            }

    def get_token_usage_stats(self) -> Dict:
        """
        获取token使用统计

        Returns:
            token使用统计信息
        """
        try:
            # 获取Memobase使用统计
            usage_stats = self.memobase_client.get_usage_stats()

            # 添加虚拟人相关信息
            usage_stats['persona_id'] = self.persona_user_id
            usage_stats['persona_name'] = self.persona_config['name']

            return usage_stats
        except Exception as e:
            logger.error(f"💥 获取token使用统计失败: {e}")
            return {
                'persona_id': self.persona_user_id,
                'error': str(e)
            }
