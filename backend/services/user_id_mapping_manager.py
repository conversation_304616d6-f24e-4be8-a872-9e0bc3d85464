"""
用户ID映射管理器
负责管理用户ID与Memobase UUID之间的映射关系
现在使用数据库存储而不是JSON文件
"""

import logging
from datetime import datetime
from typing import Dict, Optional, List
from models.database import DatabaseManager

logger = logging.getLogger(__name__)


class UserIdMappingManager:
    """用户ID映射管理器"""

    def __init__(self):
        """初始化用户ID映射管理器"""
        self.db = DatabaseManager()

        # 获取现有映射数量
        mappings = self.db.get_all_user_mappings()

        logger.info(f"✅ 用户ID映射管理器初始化完成 - 已加载 {len(mappings)} 个映射")
    
    def get_memobase_uuid(self, user_id: str) -> Optional[str]:
        """
        获取用户ID对应的Memobase UUID

        Args:
            user_id: 用户ID

        Returns:
            对应的Memobase UUID，如果不存在则返回None
        """
        memobase_uuid = self.db.get_memobase_uuid(user_id)
        if memobase_uuid:
            logger.debug(f"📋 获取用户映射: {user_id} -> {memobase_uuid}")
        return memobase_uuid
    
    def save_mapping(self, user_id: str, memobase_uuid: str):
        """
        保存用户ID到Memobase UUID的映射

        Args:
            user_id: 用户ID
            memobase_uuid: Memobase生成的UUID
        """
        try:
            # 确保用户存在于数据库中
            self.db.get_user_or_create(user_id)

            # 保存映射
            success = self.db.save_memobase_uuid(user_id, memobase_uuid)

            if success:
                logger.info(f"💾 用户ID映射已保存: {user_id} -> {memobase_uuid}")
            else:
                logger.error(f"💥 保存用户ID映射失败: 用户不存在 {user_id}")

        except Exception as e:
            logger.error(f"💥 保存用户ID映射失败: {e}")
    
    def remove_mapping(self, user_id: str) -> bool:
        """
        删除用户ID映射

        Args:
            user_id: 用户ID

        Returns:
            是否成功删除
        """
        try:
            # 获取当前映射
            memobase_uuid = self.db.get_memobase_uuid(user_id)

            if memobase_uuid:
                # 清除映射
                success = self.db.save_memobase_uuid(user_id, None)

                if success:
                    logger.info(f"🗑️ 用户ID映射已删除: {user_id} -> {memobase_uuid}")
                    return True
                else:
                    logger.error(f"💥 删除用户ID映射失败: {user_id}")
                    return False
            else:
                logger.warning(f"⚠️ 用户ID映射不存在: {user_id}")
                return False

        except Exception as e:
            logger.error(f"💥 删除用户ID映射失败: {e}")
            return False
    
    def get_all_mappings(self) -> Dict[str, str]:
        """
        获取所有用户ID映射

        Returns:
            所有映射的字典
        """
        mappings = self.db.get_all_user_mappings()
        return {mapping['user_id']: mapping['memobase_uuid'] for mapping in mappings}

    def get_mapping_stats(self) -> Dict:
        """
        获取映射统计信息

        Returns:
            映射统计信息
        """
        mappings = self.db.get_all_user_mappings()
        return {
            'total_mappings': len(mappings),
            'storage_type': 'database',
            'last_updated': datetime.now().isoformat()
        }


# 全局单例实例
_user_id_mapping_manager = None


def get_user_id_mapping_manager() -> UserIdMappingManager:
    """
    获取用户ID映射管理器单例实例
    
    Returns:
        用户ID映射管理器实例
    """
    global _user_id_mapping_manager
    if _user_id_mapping_manager is None:
        _user_id_mapping_manager = UserIdMappingManager()
    return _user_id_mapping_manager
