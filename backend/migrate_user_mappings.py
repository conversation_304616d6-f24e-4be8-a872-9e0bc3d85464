#!/usr/bin/env python3
"""
用户ID映射迁移脚本
将user_id_mappings.json中的数据迁移到chat.db数据库中
"""

import os
import json
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def migrate_user_mappings():
    """迁移用户ID映射数据"""
    
    # JSON文件路径
    json_file_path = os.path.join(
        os.path.dirname(__file__), 
        'database', 
        'user_id_mappings.json'
    )
    
    logger.info("🚀 开始用户ID映射迁移...")
    
    # 检查JSON文件是否存在
    if not os.path.exists(json_file_path):
        logger.info("📋 未找到user_id_mappings.json文件，无需迁移")
        return
    
    try:
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, dict) or 'mappings' not in data:
            logger.warning("⚠️ JSON文件格式不正确，跳过迁移")
            return
        
        mappings = data['mappings']
        logger.info(f"📋 找到 {len(mappings)} 个用户ID映射")
        
        if not mappings:
            logger.info("📋 没有映射数据需要迁移")
            return
        
        # 初始化数据库
        db = DatabaseManager()
        
        # 迁移每个映射
        migrated_count = 0
        skipped_count = 0
        
        for user_id, memobase_uuid in mappings.items():
            try:
                # 检查用户是否已存在
                existing_uuid = db.get_memobase_uuid(user_id)
                
                if existing_uuid:
                    logger.info(f"⏭️ 用户 {user_id} 已有映射，跳过")
                    skipped_count += 1
                    continue
                
                # 确保用户存在于数据库中
                db.get_user_or_create(user_id)
                
                # 保存映射
                success = db.save_memobase_uuid(user_id, memobase_uuid)
                
                if success:
                    logger.info(f"✅ 迁移成功: {user_id} -> {memobase_uuid}")
                    migrated_count += 1
                else:
                    logger.error(f"❌ 迁移失败: {user_id}")
                    
            except Exception as e:
                logger.error(f"❌ 迁移用户 {user_id} 时出错: {e}")
        
        logger.info(f"🎉 迁移完成！成功: {migrated_count}, 跳过: {skipped_count}")
        
        # 备份原JSON文件
        backup_path = json_file_path + f".backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.rename(json_file_path, backup_path)
        logger.info(f"📦 原JSON文件已备份到: {backup_path}")
        
    except Exception as e:
        logger.error(f"💥 迁移过程中出错: {e}")
        raise


def verify_migration():
    """验证迁移结果"""
    logger.info("🔍 验证迁移结果...")
    
    try:
        db = DatabaseManager()
        mappings = db.get_all_user_mappings()
        
        logger.info(f"✅ 数据库中共有 {len(mappings)} 个用户映射")
        
        for mapping in mappings:
            logger.info(f"📋 {mapping['user_id']} -> {mapping['memobase_uuid']}")
            
    except Exception as e:
        logger.error(f"💥 验证过程中出错: {e}")


if __name__ == "__main__":
    try:
        migrate_user_mappings()
        verify_migration()
        logger.info("🎊 用户ID映射迁移完成！")
        
    except Exception as e:
        logger.error(f"💥 迁移失败: {e}")
        sys.exit(1)
