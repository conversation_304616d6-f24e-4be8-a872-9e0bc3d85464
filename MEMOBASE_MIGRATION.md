# Memobase记忆系统迁移指南

## 概述

本项目已从ChromaDB向量数据库迁移到Memobase记忆系统，以简化记忆管理并提供更好的用户体验。

## 主要变化

### 1. 依赖变化
- **移除**: `chromadb==0.5.23`
- **新增**: `memobase`

### 2. 配置变化
新增Memobase相关配置项：
```env
# Memobase记忆系统配置
MEMOBASE_PROJECT_URL=http://localhost:8019
MEMOBASE_API_KEY=secret
MEMOBASE_MAX_TOKEN_SIZE=500
MEMOBASE_AUTO_FLUSH=true
```

### 3. 代码结构变化

#### 新增文件
- `backend/clients/memobase_client.py` - Memobase客户端
- `backend/services/memobase_memory_manager.py` - 基于Memobase的用户记忆管理
- `backend/services/memobase_persona_memory_manager.py` - 基于Memobase的虚拟人记忆管理

#### 修改文件
- `backend/services/conversation_engine.py` - 更新为使用Memobase记忆管理器
- `backend/app.py` - 更新API以兼容Memobase接口
- `backend/config.py` - 添加Memobase配置
- `backend/.env.example` - 添加Memobase环境变量示例

## Memobase优势

### 1. 简化架构
- 无需管理向量数据库和嵌入模型
- 自动处理记忆提取和存储
- 内置用户画像生成

### 2. 更好的记忆管理
- 时间感知记忆
- 自动记忆刷新机制
- 结构化用户画像

### 3. 易于扩展
- 支持多种数据类型
- 灵活的配置选项
- 云服务和本地部署支持

## 部署说明

### 1. 本地部署Memobase

#### 使用Docker（推荐）
```bash
# 克隆Memobase仓库
git clone https://github.com/memodb-io/memobase.git
cd memobase/src/server

# 使用Docker Compose启动
docker-compose up -d
```

#### 手动部署
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

### 2. 配置环境变量
复制并修改环境变量文件：
```bash
cp backend/.env.example backend/.env
```

编辑 `backend/.env` 文件，设置Memobase配置：
```env
MEMOBASE_PROJECT_URL=http://localhost:8019
MEMOBASE_API_KEY=secret
```

### 3. 安装新依赖
```bash
cd backend
pip install -r requirements.txt
```

### 4. 启动系统
```bash
python start.py
```

## API变化

### 记忆相关API
大部分API保持兼容，但有以下变化：

1. **记忆摘要API** (`/api/memories/summary`)
   - 现在返回Memobase生成的用户上下文

2. **记忆搜索API** (`/api/memories/search`)
   - 使用Memobase的语义搜索功能

3. **清除记忆API** (`/api/memories/clear/<user_id>`)
   - 暂不支持（Memobase特性限制）

## 兼容性说明

### 保持兼容的功能
- 用户记忆提取和存储
- 虚拟人个人记忆管理
- 记忆检索和相关性搜索
- 系统提示词生成

### 不再支持的功能
- 手动清除用户记忆
- 向量数据库统计信息
- 自定义嵌入模型

## 故障排除

### 1. Memobase连接失败
- 检查Memobase服务是否正常运行
- 验证 `MEMOBASE_PROJECT_URL` 配置
- 确认 `MEMOBASE_API_KEY` 正确

### 2. 记忆提取失败
- 检查Memobase服务日志
- 验证用户数据格式
- 确认自动刷新配置

### 3. 性能问题
- 调整 `MEMOBASE_MAX_TOKEN_SIZE` 参数
- 设置合适的 `MEMOBASE_AUTO_FLUSH` 策略

## 清理完成状态

✅ **已完成的清理工作**：
- 删除了所有向量数据库相关文件
- 删除了ChromaDB相关代码和依赖
- 删除了嵌入客户端模块
- 删除了所有测试和调试文件
- 清理了ChromaDB数据目录
- 保留了SQLite版本的个人记忆管理器
- 成功迁移到Memobase记忆系统

## 迁移检查清单

- [x] 清理旧的向量数据库代码
- [x] 更新依赖配置
- [x] 创建Memobase客户端和管理器
- [x] 更新对话引擎和API
- [x] 更新配置文件
- [ ] 安装Memobase服务
- [ ] 更新环境变量配置
- [ ] 安装新的Python依赖
- [ ] 测试记忆提取功能
- [ ] 验证API兼容性
- [ ] 检查系统日志

## 技术支持

如遇到问题，请参考：
- [Memobase官方文档](https://docs.memobase.io/)
- [Memobase GitHub仓库](https://github.com/memodb-io/memobase)
- [项目Issue页面](https://github.com/memodb-io/memobase/issues)
