from memobase import MemoBaseClient, ChatBlob

PROJECT_URL = "http://localhost:8019"
PROJECT_TOKEN = "secret"

client = MemoBaseClient(
    project_url=PROJECT_URL,
    api_key=PROJECT_TOKEN,
)

assert client.ping(), "Your Memobase server is not running"
uid = client.add_user()
u = client.get_user(uid)
print("User ID is", uid)

print("Start processing...")
messages1 = [
    {
        "role": "user",
        "content": "Hello, I'm <PERSON>",
        "created_at": "2025-01-14",
    },
    {
        "role": "assistant",
        "content": "Hi, nice to meet you, <PERSON>!",
        "alias": "<PERSON><PERSON><PERSON>",
    },
]

blob = ChatBlob(messages=messages1)
bid = u.insert(blob)
u.flush()

messages2 = [
    {
        "role": "user",
        "content": "My name is <PERSON> now.",
    },
]

blob = ChatBlob(messages=messages2)
bid = u.insert(blob)
u.flush()


events = u.event()

print("Below is recent memories:")
for e in events:
    print("-----------------")
    print("📅", e.created_at.astimezone().strftime("%Y-%m-%d %H:%M:%S"))
    for i in e.event_data.profile_delta:
        print("-", i.attributes["topic"], i.attributes["sub_topic"], i.content)
    print("-----------------")
