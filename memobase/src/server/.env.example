DATABASE_NAME="memobase"
DATABASE_USER="memobase"
DATABASE_PASSWORD="helloworld"
DATABASE_LOCATION="./db/data"
REDIS_PASSWORD="helloworld"
REDIS_LOCATION="./db/redis/data"

DATABASE_EXPORT_PORT="15432"
REDIS_EXPORT_PORT="16379"
API_EXPORT_PORT="8019"
# If you want to use Swagger with your local docker set API_HOSTS like this:
# API_HOSTS="http://0.0.0.0:8019,http://localhost:8019,https://api.memobase.dev,https://api.memobase.cn"
API_HOSTS="https://api.memobase.dev,https://api.memobase.cn"
# If you encounter any CORS-related problems with the Swagger frontend, try changing this setting to true.
USE_CORS=false

PROJECT_ID="memobase_dev"
ACCESS_TOKEN="secret"
