from openai import <PERSON>ync<PERSON><PERSON><PERSON><PERSON>
from httpx import Async<PERSON>lient
from ...env import CONFIG

_global_openai_async_client = None
_global_jina_async_client = None


def get_openai_async_client_instance() -> AsyncOpenAI:
    global _global_openai_async_client
    if _global_openai_async_client is None:
        _global_openai_async_client = AsyncOpenAI(
            base_url=CONFIG.embedding_base_url,
            api_key=CONFIG.embedding_api_key,
        )
    return _global_openai_async_client


def get_jina_async_client_instance() -> AsyncClient:
    global _global_jina_async_client
    if _global_jina_async_client is None:
        _global_jina_async_client = AsyncClient(
            base_url=CONFIG.embedding_base_url,
            headers={"Authorization": f"Bearer {CONFIG.embedding_api_key}"},
        )
    return _global_jina_async_client
