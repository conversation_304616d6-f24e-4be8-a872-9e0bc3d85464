llm_api_key: XXX
llm_base_url: https://api.openai.com/v1/
best_llm_model: gpt-4o

overwrite_user_profiles:
  - topic: "basic_info"
    sub_topics:
      - name: "name"
      - name: "age"
      - name: "gender"
      - name: "occupation"
      - name: "location"
      - name: "timezone"
        description: "user's local timezone for scheduling"
      - name: "languages"
        description: "preferred languages for communication"
      - name: "contact_info"
        description: "preferred contact methods"

  - topic: "schedule_prefs"
    sub_topics:
      - name: "work_hours"
        description: "typical working schedule"
      - name: "sleep_schedule"
        description: "usual sleep and wake times"
      - name: "meeting_prefs"
        description: "preferred meeting times and duration"
      - name: "break_times"
        description: "preferred break schedules"
      - name: "focus_hours"
        description: "times for deep work or concentration"
      - name: "reminder_freq"
        description: "how often to send reminders"

  - topic: "task_management"
    sub_topics:
      - name: "priority_rules"
        description: "how to prioritize different tasks"
      - name: "task_categories"
        description: "personal, work, health, etc."
      - name: "deadline_buffer"
        description: "preferred time buffer before deadlines"
      - name: "delegation_prefs"
        description: "tasks that can be delegated"
      - name: "recurring_tasks"
        description: "regular responsibilities"
      - name: "task_format"
        description: "preferred task presentation style"

  - topic: "productivity_settings"
    sub_topics:
      - name: "focus_mode"
        description: "do not disturb preferences"
      - name: "notification_prefs"
        description: "how and when to receive alerts"
      - name: "automation_rules"
        description: "tasks to automate"
      - name: "report_frequency"
        description: "how often to receive progress reports"
      - name: "tracking_metrics"
        description: "productivity metrics to monitor"
      - name: "tool_integrations"
        description: "preferred productivity tools"

  - topic: "lifestyle_prefs"
    sub_topics:
      - name: "diet_restr"
        description: "dietary restrictions and preferences"
      - name: "exercise_routine"
        description: "fitness schedule and preferences"
      - name: "shopping_lists"
        description: "regular shopping needs"
      - name: "travel_prefs"
        description: "travel preferences and frequent destinations"
      - name: "entertainment"
        description: "preferred leisure activities"
      - name: "budget_tracking"
        description: "expense categories and limits"

  - topic: "communication_style"
    sub_topics:
      - name: "tone_pref"
        description: "formal, casual, friendly, professional"
      - name: "response_format"
        description: "brief, detailed, bullet points, etc."
      - name: "urgency_levels"
        description: "how to handle different priority levels"
      - name: "follow_up_freq"
        description: "frequency of status updates"
      - name: "comm_channels"
        description: "preferred communication platforms"
      - name: "availability"
        description: "when to expect responses"
