---
title: Features
---

## 🚀 What does Memobase offer?
- **Memobase is a Backend**: It offers a solution for managing dynamic profiles of users for your AI. 
- **User Analysis from AI**: It helps maintain user profiles by collecting and storing various aspects of user information such as age, education, interests, and opinions. 
- **Build Personalization**: This allows developers to better understand their users and enhance user engagement with their applications.
- **Scalable and Fast**: Memobase is designed to be scalable and fast at querying.

## 🖼️ User Profile as Memory
- **Customized Slots**: Memobase allows you to customize what kinds of things you want to extract from user interactions.
- **Predictable Results**: A user profile is highly structured and predictable, which makes it easier to analyze and utilize.
- **Simple yet Enough**: Just a series of topic/subtopic(*e.g.* `interests/movies`) table for each user. Human-readable and enough to contain memories.

## 👌 Common Use Cases
- **Remembering Users**: By integrating user profiles into AI systems, Memobase allows applications to remember user-specific information, which can be used to personalize interactions.

- **User Analysis and Tracking**: Memobase provides a method to track and analyze user preferences and behaviors based on their interactions with AI, offering insights that can be used for improving user experience.

- **Targeted Advertising**: By understanding user profiles, Memobase can help in delivering personalized advertisements or recommendations that align with user interests and needs.

## 🤔 How does MemeBase work?
- Memobase operates by storing user data as "blobs" that can be inserted, retrieved, and deleted. 
- It uses a buffer zone to temporarily hold recent data, which is then flushed into long-term memory either automatically (when the buffer is full or idle) or manually (e.g., at the end of a chat session). 
- This process allows Memobase to build comprehensive user profiles over time, which can be accessed and utilized by applications to enhance user interactions.

## 💰 How much Memobase cost?
You can check the performance [here](/cost)