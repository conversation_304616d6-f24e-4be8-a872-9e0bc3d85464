---
title: 'Insert Data to a User'
openapi: post /api/v1/blobs/insert/{user_id}
---

Insert new memory data (blob) for a specific user. This endpoint handles the storage of memory data and automatically updates the user's memory buffer.

The inserted data will be processed and integrated into the user's long-term memory profile.

Memobase plans to support the following blob types:  
- `ChatBlob`: ✅ [supported](/api-reference/blobs/modal/chat).  
- `DocBlob`: 🚧 in progress  
- `ImageBlob`: 🚧 in progress  
- `CodeBlob`: 🚧 in progress  
- `TranscriptBlob`: 🚧 in progress  