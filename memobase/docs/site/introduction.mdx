---
title: Introduction
---

[Memobase](https://memobase.io) is a fast and scalable, long-term **user memory backend** for your AI.
You can integrate Memobase in your AI to provide a more personalized experience to your users.

<Frame caption="Architecture diagram of Memobase">
  <img src="/images/starter.png" />
</Frame>

We have supported the following user memories from chats:
- [x] [Profile Memory](./features/profile/)
- [x] [Event Memory](./features/event/)
- [ ] Schedule Memory
- [ ] Social Memory

Memobase is now on active development, and we are looking for early adopters to become our design partners!
[Contact us](https://github.com/memodb-io/memobase?tab=readme-ov-file#support) if you are interested

## Get Started

<CardGroup cols={3}>
  <Card
    title="QuickStart"
    icon="rocket"
    href="/quickstart"
  >
    Integrate Memobase in a few lines of code
  </Card>
  <Card
    title="Why Memobase"
    icon="question"
    href="/features"
  >
    Learn more about Memobase
  </Card>
  <Card
    title="Check APIs"
    icon="square-terminal"
    href="/api-reference"
  >
    Explore the APIs
  </Card>
</CardGroup>


## Vision
Our vision is to become the **second brain infra** of AI Applications.