---
title: Performance
---
## Overview
- Memobase is extremely fast when comes to query, because it simply return the full profiles of users.
- The final total tokens of a user profile is controllable: #profile slots X #maximum tokens size of each slots.
    - [Here](features/customization/profile) to see how to design your profile slots.
    - [Here](features/customization/full#basic-config) to see how to control the maximum tokens size of each slots.
- Insertion is relatively fast and cheap, because Memobase uses a online buffer to aggregate the recent messages and process them together.
So the overhead prompt cost is amortized.
    - [Here](features/customization/full) to see how to control the buffer token size.

## Comparing with other solutions
#### Memobase vs. [mem0](https://github.com/mem0ai/mem0)
- **Cost**: Memobase is 5x~ cheaper than mem0
- **Performance**: Memobase is 5x~ faster than mem0
- **Quality**: mem0 has more gist memories, and Memobase has structured and organized memories


The full report is available at [here](https://github.com/memodb-io/memobase/tree/docs/docs/experiments/900-chats).