---
title: Basic
---

Memobase will track the past key memories and events of the users.
```python
from memobase import MemobaseClient, ChatBlob

client = MemobaseClient(api_key="your_api_key")
uid = client.add_user()
u = client.get_user(uid)

u.insert(
    ChatBlob(
        messages=[{
                "role": "user",
                "content": "I'm <PERSON>"
        }]
    )
)

print(u.event())
```

## What is an event containing?
An event contains the following information:
- event summary, `optional`: the concise summary of user's recent experience.
- event tags, `optional`: the semantic tags of this event(*e.g.* `emotion::happy`, `goal::buy a house`), default to empty. 
Check [here](./event_tag) to design your own event tags.
- profile delta, `required`: the profile slots that are extracted in this event.
- created time, `required`

A detailed format is in [here](/api-reference/events/get_events).

### Disable event summary
If you don't want to the event summary, only need the user profile, you can set `enable_event_summary` to `false` in `config.yaml`:
```yaml
enable_event_summary: false
```

