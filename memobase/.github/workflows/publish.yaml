name: test&build

on:
  merge_group:
  workflow_dispatch:
  push:
    branches:
      - main
      - dev
    tags:
      - 'v*'
    paths:
      - 'src/server/api/**'
      - '!src/server/api/**/*.md'
  pull_request:
    branches:
      - main
      - dev
    paths:
      - 'src/server/api/**'
      - '!src/server/api/**/*.md'


env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Tests on ${{ matrix.os }} for ${{ matrix.python-version }}
    strategy:
      matrix:
        python-version: [3.11]
        os: [ubuntu-latest]
    runs-on: ${{ matrix.os }}
    permissions: 
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        working-directory: ./src/server/api
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
      - name: Start containers
        working-directory: ./src/server
        run: |
          cp .env.example .env
          cp api/config.yaml.example api/config.yaml
          sh script/up-dev.sh &
      - name: Wait for containers to start
        working-directory: ./src/server
        run: |
          until docker compose exec memobase-server-db pg_isready -U $DATABASE_USER -d $DATABASE_NAME; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 5
          done
      - name: Test with pytest
        working-directory: ./src/server/api
        run: |
          cp .env.example .env
          pytest --junit-xml=junit/test-results-${{ matrix.python-version }}.xml --cov=. --cov-report=xml:coverage-${{ matrix.python-version }}.xml tests/ -s -v
      - name: Upload pytest test results and coverage
        uses: actions/upload-artifact@v4
        with:
          name: pytest-results-${{ matrix.python-version }}
          path: |
            ./src/server/api/junit/test-results-${{ matrix.python-version }}.xml
            ./src/server/api/coverage-${{ matrix.python-version }}.xml
        if: ${{ always() }}
      - name: Stop containers
        working-directory: ./src/server
        if: ${{ always() }}
        run: |
          docker compose down

  build-image:
    needs: test
    runs-on: ubuntu-latest
    permissions: 
      contents: read
      packages: write
    env:
      SHOULD_PUSH: ${{ github.event_name == 'push' || (github.event_name == 'pull_request' && !github.event.pull_request.head.repo.fork) || github.event_name == 'workflow_dispatch' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to the Container Registry
        if: ${{ env.SHOULD_PUSH == 'true' }}
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push Docker image
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64,linux/arm64
          context: ./src/server/api
          push: ${{ env.SHOULD_PUSH == 'true' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
