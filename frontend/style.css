* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: #f7f7f7;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    margin: 0;
}

/* 微信容器 */
.wechat-container {
    width: 100%;
    max-width: 414px;
    height: 100vh;
    background: #f7f7f7;
    display: flex;
    flex-direction: column;
    position: relative;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* 微信头部导航栏 */
.wechat-header {
    background: #393a3e;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    color: white;
    position: relative;
    z-index: 100;
}

.header-left, .header-right {
    width: 60px;
    display: flex;
    justify-content: center;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.back-btn, .more-btn {
    background: none;
    border: none;
    color: white;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s;
}

.back-btn:hover, .more-btn:hover {
    background: rgba(255,255,255,0.1);
}

.contact-info {
    text-align: center;
}

.contact-name {
    font-size: 18px;
    font-weight: 500;
    color: white;
    margin-bottom: 2px;
}

.contact-status {
    font-size: 12px;
    color: rgba(255,255,255,0.7);
}

/* 微信消息区域 */
.wechat-messages {
    flex: 1;
    overflow-y: auto;
    background: #f7f7f7;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 系统消息 */
.system-message {
    display: flex;
    justify-content: center;
    margin: 16px 0;
}

.system-text {
    background: rgba(0,0,0,0.1);
    color: #999;
    font-size: 12px;
    padding: 4px 12px;
    border-radius: 12px;
}

/* 消息组 */
.message-group {
    margin-bottom: 8px;
}

.message {
    display: flex;
    margin-bottom: 8px;
    max-width: 80%;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
    margin-left: auto;
}

.assistant-message {
    align-self: flex-start;
    margin-right: auto;
}

/* 头像样式 */
.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    flex-shrink: 0;
    margin: 0 8px;
    overflow: hidden;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 消息内容 */
.message-content {
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 56px);
}

.message-bubble {
    position: relative;
    word-wrap: break-word;
    word-break: break-word;
}

.message-text {
    padding: 10px 16px;
    line-height: 1.4;
    font-size: 16px;
    border-radius: 8px;
    position: relative;
}

/* 用户消息气泡 */
.user-message .message-text {
    background: #95ec69;
    color: #000;
    border-radius: 8px 8px 2px 8px;
}

/* 助手消息气泡 */
.assistant-message .message-text {
    background: white;
    color: #000;
    border-radius: 8px 8px 8px 2px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 消息时间 */
.message-time {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
    padding: 0 4px;
}

.user-message .message-time {
    text-align: right;
}

/* 微信输入区域 */
.wechat-input {
    background: #f7f7f7;
    border-top: 1px solid #e5e5e5;
    padding: 8px 16px 8px 16px;
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
}

.input-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 48px;
}

.toolbar-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: #666;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
    flex-shrink: 0;
}

.toolbar-btn:hover {
    background: rgba(0,0,0,0.05);
}

.toolbar-btn:active {
    background: rgba(0,0,0,0.1);
}

.input-area {
    flex: 1;
    margin: 0 8px;
}

.message-input {
    width: 100%;
    min-height: 36px;
    max-height: 120px;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 16px;
    line-height: 1.4;
    background: white;
    outline: none;
    resize: none;
    transition: border-color 0.2s;
}

.message-input:focus {
    border-color: #07c160;
}

.send-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #07c160;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    background: #06ad56;
}

.send-btn:disabled {
    background: #c9c9c9;
    cursor: not-allowed;
}



/* 状态面板 */
.status-panel {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 8px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    z-index: 200;
    display: flex;
    flex-direction: column;
}

.status-panel.show {
    right: 0;
}

.status-header {
    background: #393a3e;
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
}

.close-panel {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
}

.status-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.status-item {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.affection-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.affection-bar {
    flex: 1;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.affection-progress {
    height: 100%;
    background: #07c160;
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 30%;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.login-link {
    color: #07c160;
    text-decoration: none;
    font-size: 14px;
    padding: 4px 8px;
    border: 1px solid #07c160;
    border-radius: 4px;
    transition: all 0.2s;
}

.login-link:hover {
    background: #07c160;
    color: white;
}

/* 打字指示器样式 */
.typing-indicator {
    opacity: 0.8;
}

.typing-dots {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 10px 16px;
    background: white;
    border-radius: 8px 8px 8px 2px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    min-height: 20px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #999;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: 0s; }
.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-6px);
        opacity: 1;
    }
}

/* 错误提示 */
.error-toast {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    background: #ff4757;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 12px rgba(255,71,87,0.3);
    z-index: 1000;
    max-width: 90%;
}

.error-toast button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 480px) {
    body {
        align-items: stretch;
    }

    .wechat-container {
        max-width: 100%;
        height: 100vh;
        border-radius: 0;
        box-shadow: none;
    }

    .status-panel {
        width: 100%;
        right: -100%;
    }

    .message {
        max-width: 85%;
    }

    .message-text {
        font-size: 15px;
    }
}

@media (min-width: 481px) {
    body {
        padding: 20px;
    }

    .wechat-container {
        border-radius: 12px;
        height: calc(100vh - 40px);
        max-height: 800px;
    }
}

/* 滚动条样式 */
.wechat-messages::-webkit-scrollbar {
    width: 4px;
}

.wechat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.wechat-messages::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.2);
    border-radius: 2px;
}

.wechat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.3);
}

/* 状态面板滚动条 */
.status-content::-webkit-scrollbar {
    width: 4px;
}

.status-content::-webkit-scrollbar-track {
    background: #f0f0f0;
}

.status-content::-webkit-scrollbar-thumb {
    background: #c0c0c0;
    border-radius: 2px;
}

/* 动画效果 */
.message {
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具栏按钮激活状态 */
.toolbar-btn.active {
    background: rgba(7,193,96,0.1);
    color: #07c160;
}

/* 语音录制按钮样式 */
.voice-record-btn {
    width: 100%;
    height: 36px;
    background: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 16px;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.voice-record-btn:active {
    background: #e0e0e0;
    transform: scale(0.98);
}

.voice-record-btn.recording {
    background: #ff4757;
    color: white;
    border-color: #ff4757;
}

.record-text {
    font-weight: 500;
}

/* 语音录制遮罩 */
.voice-record-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.voice-record-modal {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    min-width: 280px;
}

/* 语音波形动画 */
.voice-animation {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    height: 60px;
    margin-bottom: 20px;
    gap: 4px;
}

.voice-wave {
    width: 6px;
    background: #07c160;
    border-radius: 3px;
    animation: voiceWave 1.2s ease-in-out infinite;
}

.voice-wave:nth-child(1) {
    animation-delay: 0s;
}

.voice-wave:nth-child(2) {
    animation-delay: 0.2s;
}

.voice-wave:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes voiceWave {
    0%, 100% {
        height: 20px;
        opacity: 0.5;
    }
    50% {
        height: 50px;
        opacity: 1;
    }
}

.voice-text {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.voice-hint {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.voice-timer {
    font-size: 16px;
    font-weight: 600;
    color: #ff4757;
    font-family: 'Courier New', monospace;
}

/* 视频通话界面 */
.video-call-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.video-call-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* 主视频区域 */
.video-main {
    flex: 1;
    position: relative;
    background: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.virtual-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0;
}

/* 视频字幕 */
.video-subtitle {
    position: absolute;
    bottom: 80px;
    left: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 16px;
    line-height: 1.4;
    text-align: center;
    backdrop-filter: blur(10px);
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
}

.video-subtitle.show {
    transform: translateY(0);
    opacity: 1;
}

/* 视频状态指示器 */
.video-status {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #07c160;
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

/* 聊天记录小窗口 */
.video-chat-mini {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 280px;
    max-height: 400px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    overflow: hidden;
}

.video-chat-mini.collapsed {
    max-height: 50px;
}

.mini-header {
    background: rgba(57, 58, 62, 0.9);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
}

.mini-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mini-messages {
    max-height: 340px;
    overflow-y: auto;
    padding: 12px;
}

.mini-message {
    margin-bottom: 8px;
    font-size: 12px;
    line-height: 1.4;
}

.mini-message.user {
    text-align: right;
    color: #07c160;
}

.mini-message.assistant {
    text-align: left;
    color: #333;
}

/* 视频控制栏 */
.video-controls {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
    background: rgba(0, 0, 0, 0.6);
    padding: 15px 25px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.video-control-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    backdrop-filter: blur(10px);
}

.video-control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.video-control-btn:active {
    transform: scale(0.95);
}

.end-call-btn {
    background: #ff4757 !important;
}

.end-call-btn:hover {
    background: #ff3838 !important;
}

.voice-call-btn.active {
    background: #07c160 !important;
}

/* 小窗口滚动条 */
.mini-messages::-webkit-scrollbar {
    width: 3px;
}

.mini-messages::-webkit-scrollbar-track {
    background: transparent;
}

.mini-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

/* 语音消息样式 */
.voice-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f0f0f0;
    border-radius: 18px;
    max-width: 200px;
    cursor: pointer;
    transition: all 0.2s;
}

.voice-message:hover {
    background: #e8e8e8;
}

.voice-message.playing {
    background: #07c160;
    color: white;
}

.voice-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.voice-duration {
    font-size: 14px;
    font-weight: 500;
    min-width: 30px;
}

.voice-waveform {
    display: flex;
    align-items: center;
    gap: 2px;
    flex: 1;
}

.voice-bar {
    width: 3px;
    background: currentColor;
    border-radius: 2px;
    opacity: 0.6;
    transition: all 0.2s;
}

.voice-bar.active {
    opacity: 1;
    animation: voiceBarPulse 0.8s ease-in-out infinite;
}

@keyframes voiceBarPulse {
    0%, 100% {
        transform: scaleY(0.5);
    }
    50% {
        transform: scaleY(1);
    }
}

/* 视频消息样式 */
.video-message {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    max-width: 250px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-message video {
    width: 100%;
    height: auto;
    display: block;
}

.video-play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    transition: all 0.2s;
}

.video-message:hover .video-play-overlay {
    background: rgba(0, 0, 0, 0.8);
    transform: translate(-50%, -50%) scale(1.1);
}

/* 移动端适配 */
@media (max-width: 480px) {
    .voice-record-modal {
        margin: 0 20px;
        padding: 30px 20px;
        min-width: auto;
    }

    .video-chat-mini {
        width: 240px;
        top: 10px;
        right: 10px;
        max-height: 300px;
    }

    .video-controls {
        bottom: 20px;
        gap: 15px;
        padding: 12px 20px;
    }

    .video-control-btn {
        width: 45px;
        height: 45px;
    }

    .video-subtitle {
        bottom: 70px;
        left: 10px;
        right: 10px;
        font-size: 14px;
        padding: 10px 12px;
    }

    .voice-message {
        max-width: 160px;
    }

    .video-message {
        max-width: 200px;
    }
}

/* 工具栏按钮特殊状态 */
.toolbar-btn.voice-btn.active {
    background: rgba(255, 71, 87, 0.1);
    color: #ff4757;
}

.toolbar-btn.video-btn.active {
    background: rgba(7, 193, 96, 0.1);
    color: #07c160;
}

/* 加载动画 */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #07c160;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 平滑过渡动画 */
.wechat-input, .input-area, .toolbar-btn {
    transition: all 0.3s ease;
}

/* 语音模式切换动画 */
.input-area {
    position: relative;
    overflow: hidden;
}

.message-input, .voice-record-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 按钮悬停效果增强 */
.toolbar-btn {
    position: relative;
    overflow: hidden;
}

.toolbar-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.toolbar-btn:active::before {
    width: 100px;
    height: 100px;
}

/* 视频通话进入动画 */
.video-call-overlay {
    animation: videoCallFadeIn 0.5s ease-out;
}

@keyframes videoCallFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 语音录制遮罩动画 */
.voice-record-overlay {
    animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(5px);
    }
}

.voice-record-modal {
    animation: modalSlideUp 0.3s ease-out;
}

@keyframes modalSlideUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 改进的滚动条样式 */
.wechat-messages::-webkit-scrollbar {
    width: 6px;
}

.wechat-messages::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.wechat-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    transition: background 0.2s;
}

.wechat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.4);
}

/* 消息气泡悬停效果 */
.message-bubble {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.message:hover .message-bubble {
    transform: translateY(-1px);
}

.user-message:hover .message-bubble {
    box-shadow: 0 2px 8px rgba(7, 193, 96, 0.2);
}

.assistant-message:hover .message-bubble {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 工具栏图标旋转动画 */
.toolbar-btn.active svg {
    animation: iconPulse 0.3s ease;
}

@keyframes iconPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}
