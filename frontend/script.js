class ChatApp {
    constructor() {
        this.sessionToken = localStorage.getItem('sessionToken');
        this.userInfo = this.getUserInfo();
        this.isLoading = false;

        // 多媒体相关状态
        this.isVoiceMode = false;
        this.isVideoMode = false;
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.recordStartTime = null;
        this.recordTimer = null;

        // 检查是否有有效会话
        if (!this.sessionToken) {
            this.redirectToLogin();
            return;
        }

        this.init();
    }

    getUserInfo() {
        const userInfoStr = localStorage.getItem('userInfo');
        if (userInfoStr) {
            try {
                return JSON.parse(userInfoStr);
            } catch (e) {
                console.error('解析用户信息失败:', e);
                return null;
            }
        }
        return null;
    }

    init() {
        this.bindEvents();
        this.updateUserInterface();
        this.loadGreeting();
        this.updateCharCount();
    }

    bindEvents() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const voiceBtn = document.getElementById('voiceBtn');
        const videoBtn = document.getElementById('videoBtn');
        const voiceRecordBtn = document.getElementById('voiceRecordBtn');

        // 发送按钮点击事件
        sendButton.addEventListener('click', () => this.sendMessage());

        // 输入框回车事件
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 字符计数
        messageInput.addEventListener('input', () => this.updateCharCount());

        // 语音按钮事件
        voiceBtn.addEventListener('click', () => this.toggleVoiceMode());

        // 视频按钮事件
        videoBtn.addEventListener('click', () => this.toggleVideoMode());

        // 语音录制按钮事件
        voiceRecordBtn.addEventListener('mousedown', (e) => this.startVoiceRecord(e));
        voiceRecordBtn.addEventListener('mouseup', (e) => this.stopVoiceRecord(e));
        voiceRecordBtn.addEventListener('mouseleave', (e) => this.stopVoiceRecord(e));

        // 触摸事件支持
        voiceRecordBtn.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.startVoiceRecord(e);
        });
        voiceRecordBtn.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.stopVoiceRecord(e);
        });

        // 视频通话控制事件
        this.bindVideoCallEvents();
    }

    updateCharCount() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        // 根据输入内容启用/禁用发送按钮
        if (messageInput.value.trim()) {
            sendButton.disabled = false;
        } else {
            sendButton.disabled = true;
        }
    }

    updateUserInterface() {
        const userDisplay = document.getElementById('userDisplay');
        const contactStatus = document.getElementById('contactStatus');

        if (this.userInfo && this.sessionToken) {
            // 显示用户信息
            userDisplay.textContent = this.userInfo.nickname || this.userInfo.username || '用户';
            contactStatus.textContent = '在线';
        } else {
            // 显示游客信息
            userDisplay.textContent = '游客模式';
            contactStatus.textContent = '在线';
        }
    }

    async loadGreeting() {
        try {
            const headers = {
                'Content-Type': 'application/json',
            };

            // 如果有会话令牌，添加到请求头
            if (this.sessionToken) {
                headers['Authorization'] = `Bearer ${this.sessionToken}`;
            }

            const response = await fetch('/api/greeting', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({})
            });

            const data = await response.json();

            if (data.success) {
                // 清除默认消息，保留系统消息
                const chatMessages = document.getElementById('chatMessages');
                const systemMessage = chatMessages.querySelector('.system-message');
                chatMessages.innerHTML = '';
                if (systemMessage) {
                    chatMessages.appendChild(systemMessage);
                }

                // 添加问候消息
                this.addMessage(data.response, 'assistant', new Date());
                this.updateStatus(data.affection_level, data.persona_activity);
            } else if (response.status === 401) {
                // 认证失败，跳转到登录页面
                this.redirectToLogin();
            }
        } catch (error) {
            console.error('加载问候消息失败:', error);
        }
    }

    async sendMessage() {
        if (this.isLoading) return;

        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();

        if (!message) {
            this.showError('请输入消息内容');
            return;
        }

        // 添加用户消息到界面
        this.addMessage(message, 'user', new Date());
        messageInput.value = '';
        this.updateCharCount();

        // 显示思考状态（打字指示器）
        this.showTypingIndicator();
        this.setLoading(true);

        try {
            const headers = {
                'Content-Type': 'application/json',
            };

            // 如果有会话令牌，添加到请求头
            if (this.sessionToken) {
                headers['Authorization'] = `Bearer ${this.sessionToken}`;
            }

            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    message: message
                })
            });

            const data = await response.json();

            if (data.success) {
                // 检查是否有分段消息
                if (data.response_segments && data.response_segments.length > 1) {
                    // 分段显示消息（会自动处理打字指示器）
                    this.addSegmentedMessages(data.response_segments, new Date(data.timestamp));
                } else {
                    // 单条消息：先隐藏打字指示器，再显示消息
                    this.hideTypingIndicator();
                    await this.delay(200); // 短暂停顿
                    this.addMessage(data.response, 'assistant', new Date(data.timestamp));
                }

                // 更新状态信息
                this.updateStatus(data.affection_level, data.persona_activity);

                // 显示好感度变化
                if (data.affection_change !== 0) {
                    this.showAffectionChange(data.affection_change);
                }
            } else if (response.status === 401) {
                // 认证失败，跳转到登录页面
                this.hideTypingIndicator();
                this.redirectToLogin();
            } else {
                this.hideTypingIndicator();
                this.showError(data.error || '发送消息失败');
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            this.hideTypingIndicator();
            this.showError('网络连接失败，请检查服务器状态');
        } finally {
            this.setLoading(false);
        }
    }

    addMessage(text, sender, timestamp) {
        const chatMessages = document.getElementById('chatMessages');

        // 创建消息组
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group';

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const timeStr = this.formatTime(timestamp);

        if (sender === 'user') {
            // 用户消息：绿色气泡，右对齐，简单头像
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23667eea'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16'%3E👤%3C/text%3E%3C/svg%3E" alt="用户">
                </div>
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="message-text">${this.escapeHtml(text)}</div>
                    </div>
                    <div class="message-time">${timeStr}</div>
                </div>
            `;
        } else {
            // 助手消息：白色气泡，左对齐，沈沐心头像
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23ff9a9e'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16'%3E🌸%3C/text%3E%3C/svg%3E" alt="沈沐心">
                </div>
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="message-text">${this.escapeHtml(text)}</div>
                    </div>
                    <div class="message-time">${timeStr}</div>
                </div>
            `;
        }

        messageGroup.appendChild(messageDiv);
        chatMessages.appendChild(messageGroup);
        this.scrollToBottom();
    }

    async addSegmentedMessages(segments, timestamp) {
        /**
         * 分段显示消息，模拟微信聊天的多条消息效果
         * @param {Array} segments - 消息段落数组
         * @param {Date} timestamp - 时间戳
         */
        for (let i = 0; i < segments.length; i++) {
            const segment = segments[i];

            // 计算延迟时间：根据消息长度和是否是第一条消息
            let delay;
            if (i === 0) {
                // 第一条消息延迟稍长，模拟思考时间（打字指示器已经在显示）
                delay = Math.max(800, segment.length * 50);
            } else {
                // 后续消息延迟较短，模拟快速输入
                delay = Math.max(300, segment.length * 30);
                // 为后续消息重新显示打字指示器
                this.showTypingIndicator();
            }

            await this.delay(delay);

            // 隐藏打字指示器
            this.hideTypingIndicator();

            // 短暂停顿后显示消息
            await this.delay(200);

            // 添加消息
            this.addMessage(segment, 'assistant', timestamp);

            // 消息间的停顿（除了最后一条）
            if (i < segments.length - 1) {
                await this.delay(300);
            }
        }
    }

    showTypingIndicator() {
        /**
         * 显示打字指示器
         */
        const chatMessages = document.getElementById('chatMessages');

        // 检查是否已经有打字指示器
        if (document.getElementById('typingIndicator')) {
            return;
        }

        const typingGroup = document.createElement('div');
        typingGroup.className = 'message-group';

        const typingDiv = document.createElement('div');
        typingDiv.id = 'typingIndicator';
        typingDiv.className = 'message assistant-message typing-indicator';

        typingDiv.innerHTML = `
            <div class="message-avatar">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23ff9a9e'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16'%3E🌸%3C/text%3E%3C/svg%3E" alt="沈沐心">
            </div>
            <div class="message-content">
                <div class="message-bubble">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        `;

        typingGroup.appendChild(typingDiv);
        chatMessages.appendChild(typingGroup);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        /**
         * 隐藏打字指示器
         */
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    delay(ms) {
        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise}
         */
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    updateStatus(affectionLevel, personaActivity) {
        // 更新好感度
        const affectionValue = document.getElementById('affectionValue');
        const affectionProgress = document.getElementById('affectionProgress');

        affectionValue.textContent = affectionLevel;
        affectionProgress.style.width = `${affectionLevel}%`;

        // 更新当前活动
        if (personaActivity) {
            const currentActivity = document.getElementById('currentActivity');
            currentActivity.textContent = personaActivity.activity || '休息中';
        }
    }

    showAffectionChange(change) {
        const changeText = change > 0 ? `+${change}` : `${change}`;
        const changeColor = change > 0 ? '#4CAF50' : '#f44336';

        // 创建临时提示
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: ${changeColor};
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        toast.textContent = `好感度 ${changeText}`;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 2000);
    }

    setLoading(loading) {
        this.isLoading = loading;
        const sendButton = document.getElementById('sendButton');
        const messageInput = document.getElementById('messageInput');

        // 控制发送按钮和输入框的状态
        sendButton.disabled = loading || !messageInput.value.trim();
        messageInput.disabled = loading;
    }

    showError(message) {
        const errorToast = document.getElementById('errorToast');
        const errorMessage = document.getElementById('errorMessage');

        errorMessage.textContent = message;
        errorToast.style.display = 'flex';

        // 3秒后自动隐藏
        setTimeout(() => {
            this.hideError();
        }, 3000);
    }

    hideError() {
        const errorToast = document.getElementById('errorToast');
        errorToast.style.display = 'none';
    }

    scrollToBottom() {
        const chatMessages = document.getElementById('chatMessages');
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    formatTime(date) {
        const now = new Date();
        const messageDate = new Date(date);

        if (now.toDateString() === messageDate.toDateString()) {
            return messageDate.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            return messageDate.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    redirectToLogin() {
        // 清除本地存储
        localStorage.removeItem('sessionToken');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('userId');

        // 跳转到登录页面
        window.location.href = '/login.html';
    }

    // ==================== 多媒体功能 ====================

    toggleVoiceMode() {
        this.isVoiceMode = !this.isVoiceMode;
        const voiceBtn = document.getElementById('voiceBtn');
        const messageInput = document.getElementById('messageInput');
        const voiceRecordBtn = document.getElementById('voiceRecordBtn');

        if (this.isVoiceMode) {
            voiceBtn.classList.add('active');
            messageInput.style.display = 'none';
            voiceRecordBtn.style.display = 'flex';
            this.checkMicrophonePermission();
        } else {
            voiceBtn.classList.remove('active');
            messageInput.style.display = 'block';
            voiceRecordBtn.style.display = 'none';
        }
    }

    async checkMicrophonePermission() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop());
            console.log('麦克风权限已获取');
        } catch (error) {
            console.error('麦克风权限获取失败:', error);
            this.showError('需要麦克风权限才能使用语音功能');
            this.toggleVoiceMode(); // 关闭语音模式
        }
    }

    async startVoiceRecord() {
        if (this.isRecording) return;

        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            this.mediaRecorder = new MediaRecorder(stream);
            this.recordedChunks = [];
            this.isRecording = true;
            this.recordStartTime = Date.now();

            // 显示录音界面
            this.showVoiceRecordOverlay();

            // 更新按钮状态
            const voiceRecordBtn = document.getElementById('voiceRecordBtn');
            voiceRecordBtn.classList.add('recording');
            voiceRecordBtn.querySelector('.record-text').textContent = '松开 发送';

            // 开始录音
            this.mediaRecorder.start();

            // 录音数据处理
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            // 录音结束处理
            this.mediaRecorder.onstop = () => {
                this.processVoiceRecord();
            };

            // 开始计时
            this.startRecordTimer();

        } catch (error) {
            console.error('录音启动失败:', error);
            this.showError('录音功能启动失败，请检查麦克风权限');
        }
    }

    stopVoiceRecord() {
        if (!this.isRecording) return;

        this.isRecording = false;

        // 停止录音
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            this.mediaRecorder.stop();
        }

        // 停止所有音频轨道
        if (this.mediaRecorder && this.mediaRecorder.stream) {
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
        }

        // 隐藏录音界面
        this.hideVoiceRecordOverlay();

        // 重置按钮状态
        const voiceRecordBtn = document.getElementById('voiceRecordBtn');
        voiceRecordBtn.classList.remove('recording');
        voiceRecordBtn.querySelector('.record-text').textContent = '按住 说话';

        // 停止计时
        this.stopRecordTimer();
    }

    showVoiceRecordOverlay() {
        const overlay = document.getElementById('voiceRecordOverlay');
        overlay.style.display = 'flex';

        // 重置计时器显示
        const timer = overlay.querySelector('.voice-timer');
        timer.textContent = '00:00';
    }

    hideVoiceRecordOverlay() {
        const overlay = document.getElementById('voiceRecordOverlay');
        overlay.style.display = 'none';
    }

    startRecordTimer() {
        this.recordTimer = setInterval(() => {
            if (!this.recordStartTime) return;

            const elapsed = Math.floor((Date.now() - this.recordStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');

            const timer = document.querySelector('.voice-timer');
            if (timer) {
                timer.textContent = `${minutes}:${seconds}`;
            }

            // 最大录音时长限制（60秒）
            if (elapsed >= 60) {
                this.stopVoiceRecord();
                this.showError('录音时长不能超过60秒');
            }
        }, 1000);
    }

    stopRecordTimer() {
        if (this.recordTimer) {
            clearInterval(this.recordTimer);
            this.recordTimer = null;
        }
        this.recordStartTime = null;
    }

    async processVoiceRecord() {
        if (this.recordedChunks.length === 0) {
            this.showError('录音数据为空');
            return;
        }

        // 检查录音时长
        const duration = this.recordStartTime ? (Date.now() - this.recordStartTime) / 1000 : 0;
        if (duration < 1) {
            this.showError('录音时间太短');
            return;
        }

        try {
            // 创建音频文件
            const audioBlob = new Blob(this.recordedChunks, { type: 'audio/webm' });

            // 添加语音消息到界面
            this.addVoiceMessage(audioBlob, duration, 'user', new Date());

            // 发送语音消息到服务器
            await this.sendVoiceMessage(audioBlob);

        } catch (error) {
            console.error('语音处理失败:', error);
            this.showError('语音处理失败');
        }
    }

    addVoiceMessage(audioBlob, duration, sender, timestamp) {
        const chatMessages = document.getElementById('chatMessages');
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group';

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const timeStr = this.formatTime(timestamp);
        const durationStr = Math.floor(duration) + '"';

        // 创建音频URL
        const audioUrl = URL.createObjectURL(audioBlob);

        if (sender === 'user') {
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="voice-message" onclick="playVoiceMessage('${audioUrl}', this)">
                            <div class="voice-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                </svg>
                            </div>
                            <div class="voice-waveform">
                                ${this.generateVoiceWaveform()}
                            </div>
                            <div class="voice-duration">${durationStr}</div>
                        </div>
                    </div>
                    <div class="message-time">${timeStr}</div>
                </div>
                <div class="message-avatar">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%2307c160'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16'%3E👤%3C/text%3E%3C/svg%3E" alt="用户">
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23ff9a9e'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16'%3E🌸%3C/text%3E%3C/svg%3E" alt="沈沐心">
                </div>
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="voice-message" onclick="playVoiceMessage('${audioUrl}', this)">
                            <div class="voice-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                </svg>
                            </div>
                            <div class="voice-waveform">
                                ${this.generateVoiceWaveform()}
                            </div>
                            <div class="voice-duration">${durationStr}</div>
                        </div>
                    </div>
                    <div class="message-time">${timeStr}</div>
                </div>
            `;
        }

        messageGroup.appendChild(messageDiv);
        chatMessages.appendChild(messageGroup);
        this.scrollToBottom();
    }

    generateVoiceWaveform() {
        const bars = [];
        for (let i = 0; i < 20; i++) {
            const height = Math.random() * 20 + 5;
            bars.push(`<div class="voice-bar" style="height: ${height}px;"></div>`);
        }
        return bars.join('');
    }

    async sendVoiceMessage(audioBlob) {
        try {
            const formData = new FormData();
            formData.append('voice', audioBlob, 'voice.webm');

            const headers = {};
            if (this.sessionToken) {
                headers['Authorization'] = `Bearer ${this.sessionToken}`;
            }

            const response = await fetch('/api/chat/voice', {
                method: 'POST',
                headers: headers,
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.segments && data.segments.length > 0) {
                await this.addSegmentedMessages(data.segments, new Date());
            }

            // 更新好感度
            if (data.affection_change) {
                this.updateAffection(data.affection_change);
            }

        } catch (error) {
            console.error('发送语音消息失败:', error);
            this.showError('语音消息发送失败');
        }
    }

    toggleVideoMode() {
        this.isVideoMode = !this.isVideoMode;
        const videoBtn = document.getElementById('videoBtn');

        if (this.isVideoMode) {
            videoBtn.classList.add('active');
            this.showVideoCall();
        } else {
            videoBtn.classList.remove('active');
            this.hideVideoCall();
        }
    }

    showVideoCall() {
        const videoOverlay = document.getElementById('videoCallOverlay');
        const videoStatus = document.getElementById('videoStatus');

        videoOverlay.style.display = 'flex';
        videoStatus.querySelector('.status-text').textContent = '连接中...';

        // 同步聊天记录到小窗口
        this.syncChatToMini();

        // 模拟连接成功
        setTimeout(() => {
            videoStatus.querySelector('.status-text').textContent = '通话中';
        }, 2000);
    }

    hideVideoCall() {
        const videoOverlay = document.getElementById('videoCallOverlay');
        videoOverlay.style.display = 'none';
    }

    syncChatToMini() {
        const chatMessages = document.getElementById('chatMessages');
        const miniMessages = document.getElementById('miniMessages');

        // 清空小窗口
        miniMessages.innerHTML = '';

        // 获取最近的消息
        const messages = chatMessages.querySelectorAll('.message');
        const recentMessages = Array.from(messages).slice(-10);

        recentMessages.forEach(message => {
            const miniMessage = document.createElement('div');
            miniMessage.className = 'mini-message';

            if (message.classList.contains('user-message')) {
                miniMessage.classList.add('user');
                miniMessage.textContent = '我: ' + this.extractMessageText(message);
            } else {
                miniMessage.classList.add('assistant');
                miniMessage.textContent = '沈沐心: ' + this.extractMessageText(message);
            }

            miniMessages.appendChild(miniMessage);
        });

        // 滚动到底部
        miniMessages.scrollTop = miniMessages.scrollHeight;
    }

    extractMessageText(messageElement) {
        const textElement = messageElement.querySelector('.message-text');
        if (textElement) {
            return textElement.textContent.trim();
        }

        const voiceElement = messageElement.querySelector('.voice-message');
        if (voiceElement) {
            return '[语音消息]';
        }

        return '[消息]';
    }

    bindVideoCallEvents() {
        const muteBtn = document.getElementById('muteBtn');
        const voiceCallBtn = document.getElementById('voiceCallBtn');
        const endCallBtn = document.getElementById('endCallBtn');

        if (muteBtn) {
            muteBtn.addEventListener('click', () => this.toggleMute());
        }

        if (voiceCallBtn) {
            voiceCallBtn.addEventListener('click', () => this.toggleVoiceCall());
        }

        if (endCallBtn) {
            endCallBtn.addEventListener('click', () => this.endCall());
        }
    }

    toggleMute() {
        const muteBtn = document.getElementById('muteBtn');
        const video = document.getElementById('virtualCharacterVideo');

        if (video.muted) {
            video.muted = false;
            muteBtn.style.opacity = '1';
        } else {
            video.muted = true;
            muteBtn.style.opacity = '0.5';
        }
    }

    toggleVoiceCall() {
        const voiceCallBtn = document.getElementById('voiceCallBtn');
        voiceCallBtn.classList.toggle('active');

        if (voiceCallBtn.classList.contains('active')) {
            // 启用语音通话模式
            console.log('语音通话模式已启用');
        } else {
            // 禁用语音通话模式
            console.log('语音通话模式已禁用');
        }
    }

    endCall() {
        this.isVideoMode = false;
        const videoBtn = document.getElementById('videoBtn');
        videoBtn.classList.remove('active');
        this.hideVideoCall();
    }
}

// 微信风格的全局函数
function hideError() {
    const errorToast = document.getElementById('errorToast');
    errorToast.style.display = 'none';
}

function showChatList() {
    // 模拟返回聊天列表
    alert('返回聊天列表功能开发中...');
}

function showMoreOptions() {
    // 显示状态面板
    const statusPanel = document.getElementById('statusPanel');
    statusPanel.classList.add('show');
}

function hideStatusPanel() {
    const statusPanel = document.getElementById('statusPanel');
    statusPanel.classList.remove('show');
}

function showUserProfile() {
    alert('用户资料功能开发中...');
}

function logout() {
    if (confirm('确定要登出吗？')) {
        // 清除本地存储
        localStorage.removeItem('sessionToken');
        localStorage.removeItem('userInfo');

        // 重新加载页面
        window.location.reload();
    }
}

// 全局语音播放函数
function playVoiceMessage(audioUrl, element) {
    // 停止其他正在播放的语音
    const allVoiceMessages = document.querySelectorAll('.voice-message');
    allVoiceMessages.forEach(msg => {
        msg.classList.remove('playing');
        const bars = msg.querySelectorAll('.voice-bar');
        bars.forEach(bar => bar.classList.remove('active'));
    });

    // 创建音频对象
    const audio = new Audio(audioUrl);

    // 标记为正在播放
    element.classList.add('playing');
    const bars = element.querySelectorAll('.voice-bar');

    // 播放动画
    let barIndex = 0;
    const animationInterval = setInterval(() => {
        bars.forEach(bar => bar.classList.remove('active'));
        if (barIndex < bars.length) {
            bars[barIndex].classList.add('active');
            barIndex = (barIndex + 1) % bars.length;
        }
    }, 100);

    // 播放音频
    audio.play().catch(error => {
        console.error('音频播放失败:', error);
        element.classList.remove('playing');
        clearInterval(animationInterval);
    });

    // 播放结束处理
    audio.onended = () => {
        element.classList.remove('playing');
        bars.forEach(bar => bar.classList.remove('active'));
        clearInterval(animationInterval);
    };

    // 播放错误处理
    audio.onerror = () => {
        element.classList.remove('playing');
        bars.forEach(bar => bar.classList.remove('active'));
        clearInterval(animationInterval);
        console.error('音频播放出错');
    };
}

// 切换视频聊天小窗口
function toggleChatMini() {
    const miniWindow = document.getElementById('videoChatMini');
    const toggleBtn = miniWindow.querySelector('.mini-toggle');

    if (miniWindow.classList.contains('collapsed')) {
        miniWindow.classList.remove('collapsed');
        toggleBtn.textContent = '−';
    } else {
        miniWindow.classList.add('collapsed');
        toggleBtn.textContent = '+';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
